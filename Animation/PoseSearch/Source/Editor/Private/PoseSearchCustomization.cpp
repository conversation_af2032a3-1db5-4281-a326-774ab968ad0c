// Copyright Epic Games, Inc. All Rights Reserved.

#include "PoseSearchCustomization.h"
#include "Engine/GameViewportClient.h"
#include "AssetRegistry/AssetData.h"
#include "EditorClassUtils.h"
#include "IPropertyUtilities.h"
#include "PropertyHandle.h"
#include "PropertyCustomizationHelpers.h"
#include "ScopedTransaction.h"
#include "Engine/EngineBaseTypes.h"
#include "UObject/UnrealType.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Layout/SWrapBox.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Images/SImage.h"
#include "Widgets/Input/SButton.h"
#include "IDetailChildrenBuilder.h"
#include "DetailLayoutBuilder.h"
#include "DetailWidgetRow.h"
#include "DetailCategoryBuilder.h"
#include "IDetailsView.h"
#include "ObjectEditorUtils.h"
#include "Animation/AnimSequence.h"
#include "PoseSearch/PoseSearchDatabase.h"

#define LOCTEXT_NAMESPACE "PoseSearchCustomization"


///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// FPoseSearchDatabaseSequenceCustomization

void FPoseSearchDatabaseSequenceCustomization::CustomizeHeader(TSharedRef<IPropertyHandle> InStructPropertyHandle, FDetailWidgetRow& HeaderRow, IPropertyTypeCustomizationUtils& StructCustomizationUtils)
{
	FText SequenceNameText;

	TArray<UObject*> Objects;
	InStructPropertyHandle->GetOuterObjects(Objects);

	if (Objects.Num() == 1)
	{
		FPoseSearchDatabaseSequence* PoseSearchDatabaseSequence = (FPoseSearchDatabaseSequence*)InStructPropertyHandle->GetValueBaseAddress((uint8*)Objects[0]);
		check(PoseSearchDatabaseSequence);
		
		if (const UObject* AnimationAsset = PoseSearchDatabaseSequence->GetAnimationAsset())
		{
			SequenceNameText = FText::FromName(AnimationAsset->GetFName());
		}
		else
		{
			SequenceNameText = LOCTEXT("NewSequenceLabel", "New Sequence");
		}
	}

	HeaderRow
	.NameContent()
	[
		SNew(STextBlock)
		.Text(SequenceNameText)
	];

	const FSimpleDelegate OnValueChanged = FSimpleDelegate::CreateLambda([&StructCustomizationUtils]()
	{
		StructCustomizationUtils.GetPropertyUtilities()->ForceRefresh();
	});

	InStructPropertyHandle->SetOnChildPropertyValueChanged(OnValueChanged);
}

void FPoseSearchDatabaseSequenceCustomization::CustomizeChildren(TSharedRef<IPropertyHandle> InStructPropertyHandle, IDetailChildrenBuilder& StructBuilder, IPropertyTypeCustomizationUtils& StructCustomizationUtils)
{
	uint32 NumChildren;
	InStructPropertyHandle->GetNumChildren(NumChildren);

	for (uint32 ChildIndex = 0; ChildIndex < NumChildren; ++ChildIndex)
	{
		StructBuilder.AddProperty(InStructPropertyHandle->GetChildHandle(ChildIndex).ToSharedRef());
	}
}

#undef LOCTEXT_NAMESPACE

