// Copyright Epic Games, Inc. All Rights Reserved.

#include "dna/Reader.h"

namespace dna {

HeaderReader::~HeaderReader() = default;
DescriptorReader::~DescriptorReader() = default;
DefinitionReader::~DefinitionReader() = default;
BehaviorReader::~BehaviorReader() = default;
GeometryReader::~GeometryReader() = default;
MachineLearnedBehaviorReader::~MachineLearnedBehaviorReader() = default;
RBFBehaviorReader::~RBFBehaviorReader() = default;
TwistSwingBehaviorReader::~TwistSwingBehaviorReader() = default;
JointBehaviorMetadataReader::~JointBehaviorMetadataReader() = default;
Reader::~Reader() = default;

}  // namespace dna
