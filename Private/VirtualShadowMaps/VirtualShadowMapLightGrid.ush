// Copyright Epic Games, Inc. All Rights Reserved.

/*=============================================================================
VirtualShadowMapLightGrid.ush:
=============================================================================*/
#pragma once

#include "../Common.ush"
#include "../LightGridCommon.ush"

FCulledLightsGridHeader VirtualShadowMapGetLightsGrid(uint2 PixelPos, float SceneDepth)
{
	// Has no effect unless INSTANCED_STEREO is enabled. Used in GetLightGridData to select ForwardLightData or ForwardLightDataISR, but then only uses redundant parameters anyway.
	uint EyeIndex = 0U;

	// Index 0 is always going to a valid index and the NumCulledLightsGrid is initialized to zero making acess safe.
	uint GridLinearIndex = min(VirtualShadowMap.MaxLightGridEntryIndex, ComputeLightGridCellIndex(PixelPos, SceneDepth, EyeIndex));
	FCulledLightsGridHeader CulledLightGridHeader = GetCulledLightsGridHeader(GridLinearIndex, EyeIndex);

	// Replace light count with our pruned count
	checkStructuredBufferAccessSlow(VirtualShadowMap.NumCulledLightsGrid, GridLinearIndex);
	CulledLightGridHeader.NumLights = VirtualShadowMap.NumCulledLightsGrid[GridLinearIndex];
	return CulledLightGridHeader;
}

FLocalLightData VirtualShadowMapGetLocalLightData(FCulledLightsGridHeader GridHeader, uint Index)
{
	const uint ListIndex = GridHeader.DataStartIndex + Index;
	checkStructuredBufferAccessSlow(VirtualShadowMap.LightGridData, ListIndex);
	const uint LightGridLightIndex = VirtualShadowMap.LightGridData[ListIndex];
	return GetLocalLightDataNonStereo(LightGridLightIndex);
}
