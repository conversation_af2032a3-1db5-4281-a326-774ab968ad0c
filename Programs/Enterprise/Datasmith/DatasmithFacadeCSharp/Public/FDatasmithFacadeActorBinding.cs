// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeActorBinding : FDatasmithFacadeElement {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;

  internal FDatasmithFacadeActorBinding(global::System.IntPtr cPtr, bool cMemoryOwn) : base(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_SWIGUpcast(cPtr), cMemoryOwn) {
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeActorBinding obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  protected override void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeActorBinding(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
      base.Dispose(disposing);
    }
  }

  public FDatasmithFacadeActorBinding(FDatasmithFacadeActor InActorPtr) : this(DatasmithFacadeCSharpPINVOKE.new_FDatasmithFacadeActorBinding(FDatasmithFacadeActor.getCPtr(InActorPtr)), true) {
  }

  public void AddPropertyCapture(FDatasmithFacadePropertyCapture InPropertyCapturePtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddPropertyCapture(swigCPtr, FDatasmithFacadePropertyCapture.getCPtr(InPropertyCapturePtr));
  }

  public int GetPropertyCapturesCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_GetPropertyCapturesCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadePropertyCapture GetPropertyCapture(int PropertyCaptureIndex) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_GetPropertyCapture(swigCPtr, PropertyCaptureIndex);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadePropertyCapture(objectPtr, true);
	}
}

  public void RemovePropertyCapture(FDatasmithFacadePropertyCapture InPropertyCapturePtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_RemovePropertyCapture(swigCPtr, FDatasmithFacadePropertyCapture.getCPtr(InPropertyCapturePtr));
  }

  public void AddRelativeLocationCapture(double X, double Y, double Z) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddRelativeLocationCapture(swigCPtr, X, Y, Z);
  }

  public void AddRelativeRotationCapture(double Pitch, double Yaw, double Roll) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddRelativeRotationCapture__SWIG_0(swigCPtr, Pitch, Yaw, Roll);
  }

  public void AddRelativeRotationCapture(double X, double Y, double Z, double W) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddRelativeRotationCapture__SWIG_1(swigCPtr, X, Y, Z, W);
  }

  public void AddRelativeScaleCapture(double X, double Y, double Z) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddRelativeScaleCapture(swigCPtr, X, Y, Z);
  }

  public void AddRelativeTransformCapture(double[] InMatrix, bool bRowMajor) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddRelativeTransformCapture__SWIG_0(swigCPtr, InMatrix, bRowMajor);
  }

  public void AddRelativeTransformCapture(double[] InMatrix) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddRelativeTransformCapture__SWIG_1(swigCPtr, InMatrix);
  }

  public void AddRelativeTransformCapture(float[] InMatrix, bool bRowMajor) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddRelativeTransformCapture__SWIG_2(swigCPtr, InMatrix, bRowMajor);
  }

  public void AddRelativeTransformCapture(float[] InMatrix) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddRelativeTransformCapture__SWIG_3(swigCPtr, InMatrix);
  }

  public void AddVisibilityCapture(bool bInVisibility) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddVisibilityCapture(swigCPtr, bInVisibility);
  }

  public void AddMaterialCapture(FDatasmithFacadeBaseMaterial InMaterialPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeActorBinding_AddMaterialCapture(swigCPtr, FDatasmithFacadeBaseMaterial.getCPtr(InMaterialPtr));
  }

}
