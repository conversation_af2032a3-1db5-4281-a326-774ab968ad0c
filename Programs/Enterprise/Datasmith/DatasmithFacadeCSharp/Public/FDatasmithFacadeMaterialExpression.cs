// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeMaterialExpression : global::System.IDisposable {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;
  protected bool swigCMemOwn;

  internal FDatasmithFacadeMaterialExpression(global::System.IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeMaterialExpression obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  ~FDatasmithFacadeMaterialExpression() {
    Dispose(false);
  }

  public void Dispose() {
    Dispose(true);
    global::System.GC.SuppressFinalize(this);
  }

  protected virtual void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeMaterialExpression(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
    }
  }

  public string GetName() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_GetName(swigCPtr));
    return ret;
  }

  public void SetName(string InName) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_SetName(swigCPtr, InName);
  }

  public EDatasmithFacadeMaterialExpressionType GetExpressionType() {
    EDatasmithFacadeMaterialExpressionType ret = (EDatasmithFacadeMaterialExpressionType)DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_GetExpressionType(swigCPtr);
    return ret;
  }

  public void ConnectExpression(FDatasmithFacadeExpressionInput ExpressionInput) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_ConnectExpression__SWIG_0(swigCPtr, FDatasmithFacadeExpressionInput.getCPtr(ExpressionInput));
    if (DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Pending) throw DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Retrieve();
  }

  public void ConnectExpression(FDatasmithFacadeExpressionInput ExpressionInput, int OutputIndex) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_ConnectExpression__SWIG_1(swigCPtr, FDatasmithFacadeExpressionInput.getCPtr(ExpressionInput), OutputIndex);
    if (DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Pending) throw DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Retrieve();
  }

  public int GetInputCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_GetInputCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetInput(int Index) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_GetInput(swigCPtr, Index);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadeExpressionInput(objectPtr, true);
	}
}

  public int GetDefaultOutputIndex() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_GetDefaultOutputIndex(swigCPtr);
    return ret;
  }

  public void SetDefaultOutputIndex(int OutputIndex) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_SetDefaultOutputIndex(swigCPtr, OutputIndex);
  }

  public void ResetExpression() {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpression_ResetExpression(swigCPtr);
  }

}
