// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeMaterialExpressionBool : FDatasmithFacadeMaterialExpression, FDatasmithFacadeExpressionParameter {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;

  internal FDatasmithFacadeMaterialExpressionBool(global::System.IntPtr cPtr, bool cMemoryOwn) : base(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpressionBool_SWIGUpcast(cPtr), cMemoryOwn) {
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeMaterialExpressionBool obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  protected override void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeMaterialExpressionBool(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
      base.Dispose(disposing);
    }
  }

  [System.ComponentModel.EditorBrowsable(System.ComponentModel.EditorBrowsableState.Never)]
  global::System.Runtime.InteropServices.HandleRef FDatasmithFacadeExpressionParameter.GetInterfaceCPtr() {
    return new global::System.Runtime.InteropServices.HandleRef(this, DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpressionBool_FDatasmithFacadeExpressionParameter_GetInterfaceCPtr(swigCPtr.Handle));
  }

  public bool GetBool() {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpressionBool_GetBool(swigCPtr);
    return ret;
  }

  public void SetBool(bool InValue) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpressionBool_SetBool(swigCPtr, InValue);
  }

  public virtual string GetGroupName() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpressionBool_GetGroupName(swigCPtr));
    return ret;
  }

  public virtual void SetGroupName(string InGroupName) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeMaterialExpressionBool_SetGroupName(swigCPtr, InGroupName);
  }

}
