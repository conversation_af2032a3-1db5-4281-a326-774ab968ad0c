// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeScene : global::System.IDisposable {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;
  protected bool swigCMemOwn;

  internal FDatasmithFacadeScene(global::System.IntPtr cPtr, bool cMemoryOwn) {
    swigCMemOwn = cMemoryOwn;
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeScene obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  ~FDatasmithFacadeScene() {
    Dispose(false);
  }

  public void Dispose() {
    Dispose(true);
    global::System.GC.SuppressFinalize(this);
  }

  protected virtual void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeScene(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
    }
  }

  public FDatasmithFacadeScene(string InApplicationHostName, string InApplicationVendorName, string InApplicationProductName, string InApplicationProductVersion) : this(DatasmithFacadeCSharpPINVOKE.new_FDatasmithFacadeScene(InApplicationHostName, InApplicationVendorName, InApplicationProductName, InApplicationProductVersion), true) {
  }

  public void AddActor(FDatasmithFacadeActor InActorPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_AddActor(swigCPtr, FDatasmithFacadeActor.getCPtr(InActorPtr));
  }

  public int GetActorsCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetActorsCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeActor GetActor(int ActorIndex) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetActor(swigCPtr, ActorIndex);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		FDatasmithFacadeActor.EActorType ActorType = (new FDatasmithFacadeActor(objectPtr, false)).GetActorType();

		switch(ActorType)
		{
		case FDatasmithFacadeActor.EActorType.DirectionalLight:
			return new FDatasmithFacadeDirectionalLight(objectPtr, true);
		case FDatasmithFacadeActor.EActorType.AreaLight:
			return new FDatasmithFacadeAreaLight(objectPtr, true);
		case FDatasmithFacadeActor.EActorType.LightmassPortal:
			return new FDatasmithFacadeLightmassPortal(objectPtr, true);
		case FDatasmithFacadeActor.EActorType.PointLight:
			return new FDatasmithFacadePointLight(objectPtr, true);
		case FDatasmithFacadeActor.EActorType.SpotLight:
			return new FDatasmithFacadeSpotLight(objectPtr, true);
		case FDatasmithFacadeActor.EActorType.StaticMeshActor:
			return new FDatasmithFacadeActorMesh(objectPtr, true);
		case FDatasmithFacadeActor.EActorType.Camera:
			return new FDatasmithFacadeActorCamera(objectPtr, true);
		case FDatasmithFacadeActor.EActorType.Decal:
			return new FDatasmithFacadeActorDecal(objectPtr, true);
		case FDatasmithFacadeActor.EActorType.Actor:
			return new FDatasmithFacadeActor(objectPtr, true);
		case FDatasmithFacadeActor.EActorType.Unsupported:
		default:
			return null;
		}
	}
}

  public void RemoveActor(FDatasmithFacadeActor InActorPtr, FDatasmithFacadeScene.EActorRemovalRule RemovalRule) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveActor__SWIG_0(swigCPtr, FDatasmithFacadeActor.getCPtr(InActorPtr), (int)RemovalRule);
  }

  public void RemoveActor(FDatasmithFacadeActor InActorPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveActor__SWIG_1(swigCPtr, FDatasmithFacadeActor.getCPtr(InActorPtr));
  }

  public void RemoveActorAt(int ActorIndex, FDatasmithFacadeScene.EActorRemovalRule RemovalRule) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveActorAt__SWIG_0(swigCPtr, ActorIndex, (int)RemovalRule);
  }

  public void RemoveActorAt(int ActorIndex) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveActorAt__SWIG_1(swigCPtr, ActorIndex);
  }

  public void AddMaterial(FDatasmithFacadeBaseMaterial InMaterialPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_AddMaterial(swigCPtr, FDatasmithFacadeBaseMaterial.getCPtr(InMaterialPtr));
  }

  public int GetMaterialsCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetMaterialsCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeBaseMaterial GetMaterial(int MaterialIndex) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetMaterial(swigCPtr, MaterialIndex);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		FDatasmithFacadeBaseMaterial.EDatasmithMaterialType MaterialType = (new FDatasmithFacadeBaseMaterial(objectPtr, false)).GetDatasmithMaterialType();

		switch(MaterialType)
		{
		case FDatasmithFacadeBaseMaterial.EDatasmithMaterialType.UEPbrMaterial:
			return new FDatasmithFacadeUEPbrMaterial(objectPtr, true);
		case FDatasmithFacadeBaseMaterial.EDatasmithMaterialType.MaterialInstance:
			return new FDatasmithFacadeMaterialInstance(objectPtr, true);
		case FDatasmithFacadeBaseMaterial.EDatasmithMaterialType.DecalMaterial:
			return new FDatasmithFacadeDecalMaterial(objectPtr, true);
		case FDatasmithFacadeBaseMaterial.EDatasmithMaterialType.Unsupported:
		default:
			return null;
		}
	}
}

  public void RemoveMaterial(FDatasmithFacadeBaseMaterial InMaterialPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveMaterial(swigCPtr, FDatasmithFacadeBaseMaterial.getCPtr(InMaterialPtr));
  }

  public void RemoveMaterialAt(int MaterialIndex) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveMaterialAt(swigCPtr, MaterialIndex);
  }

  public FDatasmithFacadeMeshElement ExportDatasmithMesh(FDatasmithFacadeMesh Mesh, FDatasmithFacadeMesh CollisionMesh) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_ExportDatasmithMesh__SWIG_0(swigCPtr, FDatasmithFacadeMesh.getCPtr(Mesh), FDatasmithFacadeMesh.getCPtr(CollisionMesh));
    FDatasmithFacadeMeshElement ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeMeshElement(cPtr, false);
    return ret;
  }

  public FDatasmithFacadeMeshElement ExportDatasmithMesh(FDatasmithFacadeMesh Mesh) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_ExportDatasmithMesh__SWIG_1(swigCPtr, FDatasmithFacadeMesh.getCPtr(Mesh));
    FDatasmithFacadeMeshElement ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeMeshElement(cPtr, false);
    return ret;
  }

  public bool ExportDatasmithMesh(FDatasmithFacadeMeshElement MeshElement, FDatasmithFacadeMesh Mesh, FDatasmithFacadeMesh CollisionMesh) {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_ExportDatasmithMesh__SWIG_2(swigCPtr, FDatasmithFacadeMeshElement.getCPtr(MeshElement), FDatasmithFacadeMesh.getCPtr(Mesh), FDatasmithFacadeMesh.getCPtr(CollisionMesh));
    return ret;
  }

  public bool ExportDatasmithMesh(FDatasmithFacadeMeshElement MeshElement, FDatasmithFacadeMesh Mesh) {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_ExportDatasmithMesh__SWIG_3(swigCPtr, FDatasmithFacadeMeshElement.getCPtr(MeshElement), FDatasmithFacadeMesh.getCPtr(Mesh));
    return ret;
  }

  public void AddMesh(FDatasmithFacadeMeshElement InMeshPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_AddMesh(swigCPtr, FDatasmithFacadeMeshElement.getCPtr(InMeshPtr));
  }

  public int GetMeshesCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetMeshesCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeMeshElement GetMesh(int MeshIndex) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetMesh(swigCPtr, MeshIndex);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadeMeshElement(objectPtr, true);
	}
}

  public void RemoveMesh(FDatasmithFacadeMeshElement MeshElement) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveMesh(swigCPtr, FDatasmithFacadeMeshElement.getCPtr(MeshElement));
  }

  public void RemoveMeshAt(int MeshIndex) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveMeshAt(swigCPtr, MeshIndex);
  }

  public void AddTexture(FDatasmithFacadeTexture InTexturePtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_AddTexture(swigCPtr, FDatasmithFacadeTexture.getCPtr(InTexturePtr));
  }

  public int GetTexturesCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetTexturesCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeTexture GetTexture(int TextureIndex) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetTexture(swigCPtr, TextureIndex);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadeTexture(objectPtr, true);
	}
}

  public void RemoveTexture(FDatasmithFacadeTexture InTexturePtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveTexture(swigCPtr, FDatasmithFacadeTexture.getCPtr(InTexturePtr));
  }

  public void RemoveTextureAt(int TextureIndex) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveTextureAt(swigCPtr, TextureIndex);
  }

  public void AddLevelVariantSets(FDatasmithFacadeLevelVariantSets InLevelVariantSetsPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_AddLevelVariantSets(swigCPtr, FDatasmithFacadeLevelVariantSets.getCPtr(InLevelVariantSetsPtr));
  }

  public int GetLevelVariantSetsCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetLevelVariantSetsCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeLevelVariantSets GetLevelVariantSets(int LevelVariantSetsIndex) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetLevelVariantSets(swigCPtr, LevelVariantSetsIndex);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadeLevelVariantSets(objectPtr, true);
	}
}

  public void RemoveLevelVariantSets(FDatasmithFacadeLevelVariantSets InLevelVariantSetsPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveLevelVariantSets(swigCPtr, FDatasmithFacadeLevelVariantSets.getCPtr(InLevelVariantSetsPtr));
  }

  public void RemoveLevelVariantSetsAt(int LevelVariantSetsIndex) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveLevelVariantSetsAt(swigCPtr, LevelVariantSetsIndex);
  }

  public void AddLevelSequence(FDatasmithFacadeLevelSequence InLevelSequence) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_AddLevelSequence(swigCPtr, FDatasmithFacadeLevelSequence.getCPtr(InLevelSequence));
  }

  public int GetLevelSequencesCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetLevelSequencesCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeLevelSequence GetLevelSequence(int LevelSequenceIndex) {
    global::System.IntPtr cPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetLevelSequence(swigCPtr, LevelSequenceIndex);
    FDatasmithFacadeLevelSequence ret = (cPtr == global::System.IntPtr.Zero) ? null : new FDatasmithFacadeLevelSequence(cPtr, false);
    return ret;
  }

  public void RemoveLevelSequence(FDatasmithFacadeLevelSequence InLevelSequence) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveLevelSequence(swigCPtr, FDatasmithFacadeLevelSequence.getCPtr(InLevelSequence));
  }

  public void RemoveLevelSequenceAt(int LevelSequenceIndex) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveLevelSequenceAt(swigCPtr, LevelSequenceIndex);
  }

  public void AddMetaData(FDatasmithFacadeMetaData InMetaDataPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_AddMetaData(swigCPtr, FDatasmithFacadeMetaData.getCPtr(InMetaDataPtr));
  }

  public int GetMetaDataCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetMetaDataCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeMetaData GetMetaData(int MetaDataIndex) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetMetaData__SWIG_0(swigCPtr, MetaDataIndex);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadeMetaData(objectPtr, true);
	}
}

  public FDatasmithFacadeMetaData GetMetaData(FDatasmithFacadeElement Element) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetMetaData__SWIG_1(swigCPtr, FDatasmithFacadeElement.getCPtr(Element));
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		return new FDatasmithFacadeMetaData(objectPtr, true);
	}
}

  public void RemoveMetaData(FDatasmithFacadeMetaData InMetaDataPtr) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveMetaData(swigCPtr, FDatasmithFacadeMetaData.getCPtr(InMetaDataPtr));
  }

  public void RemoveMetaDataAt(int MetaDataIndex) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_RemoveMetaDataAt(swigCPtr, MetaDataIndex);
  }

  public void SetName(string InName) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetName(swigCPtr, InName);
  }

  public string GetName() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetName(swigCPtr));
    return ret;
  }

  public void SetOutputPath(string InOutputPath) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetOutputPath(swigCPtr, InOutputPath);
  }

  public string GetOutputPath() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetOutputPath(swigCPtr));
    return ret;
  }

  public string GetAssetsOutputPath() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetAssetsOutputPath(swigCPtr));
    return ret;
  }

  public void GetGeolocation(out double OutLatitude, out double OutLongitude, out double OutElevation) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetGeolocation(swigCPtr, out OutLatitude, out OutLongitude, out OutElevation);
  }

  public void SetGeolocationLatitude(double Latitude) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetGeolocationLatitude(swigCPtr, Latitude);
  }

  public void SetGeolocationLongitude(double Longitude) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetGeolocationLongitude(swigCPtr, Longitude);
  }

  public void SetGeolocationElevation(double Elevation) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetGeolocationElevation(swigCPtr, Elevation);
  }

  public void PreExport() {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_PreExport(swigCPtr);
  }

  public void CleanUp() {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_CleanUp(swigCPtr);
  }

  public static void Shutdown() {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_Shutdown();
  }

  public bool ExportScene(string InOutputPath, bool bCleanupUnusedElements) {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_ExportScene__SWIG_0(swigCPtr, InOutputPath, bCleanupUnusedElements);
    return ret;
  }

  public bool ExportScene(string InOutputPath) {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_ExportScene__SWIG_1(swigCPtr, InOutputPath);
    return ret;
  }

  public bool ExportScene(bool bCleanupUnusedElements) {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_ExportScene__SWIG_2(swigCPtr, bCleanupUnusedElements);
    return ret;
  }

  public bool ExportScene() {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_ExportScene__SWIG_3(swigCPtr);
    return ret;
  }

  public void SerializeLevelSequences() {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SerializeLevelSequences(swigCPtr);
  }

  public void SetLabel(string InSceneLabel) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetLabel(swigCPtr, InSceneLabel);
  }

  public string GetLabel() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetLabel(swigCPtr));
    return ret;
  }

  public void Reset() {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_Reset(swigCPtr);
  }

  public string GetHost() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetHost(swigCPtr));
    return ret;
  }

  public void SetHost(string InHost) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetHost(swigCPtr, InHost);
  }

  public string GetVendor() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetVendor(swigCPtr));
    return ret;
  }

  public void SetVendor(string InApplicationVendorName) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetVendor(swigCPtr, InApplicationVendorName);
  }

  public string GetProductName() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetProductName(swigCPtr));
    return ret;
  }

  public void SetProductName(string InApplicationProductName) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetProductName(swigCPtr, InApplicationProductName);
  }

  public string GetProductVersion() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_GetProductVersion(swigCPtr));
    return ret;
  }

  public void SetProductVersion(string InApplicationProductVersion) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeScene_SetProductVersion(swigCPtr, InApplicationProductVersion);
  }

  public enum EActorRemovalRule {
    RemoveChildren,
    KeepChildrenAndKeepRelativeTransform
  }

}
