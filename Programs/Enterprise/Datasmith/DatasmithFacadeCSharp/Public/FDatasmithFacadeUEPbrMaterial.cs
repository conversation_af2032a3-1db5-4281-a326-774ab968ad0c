// Copyright Epic Games, Inc. All Rights Reserved.

//------------------------------------------------------------------------------
// <auto-generated />
//
// This file was automatically generated by SWIG (http://www.swig.org).
// Version 4.0.1
//
// Do not make changes to this file unless you know what you are doing--modify
// the SWIG interface file instead.
//------------------------------------------------------------------------------


public class FDatasmithFacadeUEPbrMaterial : FDatasmithFacadeBaseMaterial {
  private global::System.Runtime.InteropServices.HandleRef swigCPtr;

  internal FDatasmithFacadeUEPbrMaterial(global::System.IntPtr cPtr, bool cMemoryOwn) : base(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_SWIGUpcast(cPtr), cMemoryOwn) {
    swigCPtr = new global::System.Runtime.InteropServices.HandleRef(this, cPtr);
  }

  internal static global::System.Runtime.InteropServices.HandleRef getCPtr(FDatasmithFacadeUEPbrMaterial obj) {
    return (obj == null) ? new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero) : obj.swigCPtr;
  }

  protected override void Dispose(bool disposing) {
    lock(this) {
      if (swigCPtr.Handle != global::System.IntPtr.Zero) {
        if (swigCMemOwn) {
          swigCMemOwn = false;
          DatasmithFacadeCSharpPINVOKE.delete_FDatasmithFacadeUEPbrMaterial(swigCPtr);
        }
        swigCPtr = new global::System.Runtime.InteropServices.HandleRef(null, global::System.IntPtr.Zero);
      }
      base.Dispose(disposing);
    }
  }

  public FDatasmithFacadeUEPbrMaterial(string InElementName) : this(DatasmithFacadeCSharpPINVOKE.new_FDatasmithFacadeUEPbrMaterial(InElementName), true) {
  }

  public FDatasmithFacadeExpressionInput GetBaseColor() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetBaseColor(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetMetallic() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetMetallic(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetSpecular() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetSpecular(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetRoughness() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetRoughness(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetEmissiveColor() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetEmissiveColor(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetOpacity() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetOpacity(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetNormal() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetNormal(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetRefraction() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetRefraction(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetAmbientOcclusion() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetAmbientOcclusion(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetClearCoat() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetClearCoat(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetClearCoatRoughness() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetClearCoatRoughness(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetWorldPositionOffset() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetWorldPositionOffset(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeExpressionInput GetMaterialAttributes() {
    FDatasmithFacadeExpressionInput ret = new FDatasmithFacadeExpressionInput(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetMaterialAttributes(swigCPtr), true);
    return ret;
  }

  public int GetBlendMode() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetBlendMode(swigCPtr);
    return ret;
  }

  public void SetBlendMode(int bInBlendMode) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_SetBlendMode(swigCPtr, bInBlendMode);
  }

  public bool GetTwoSided() {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetTwoSided(swigCPtr);
    return ret;
  }

  public void SetTwoSided(bool bTwoSided) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_SetTwoSided(swigCPtr, bTwoSided);
  }

  public bool GetUseMaterialAttributes() {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetUseMaterialAttributes(swigCPtr);
    return ret;
  }

  public void SetUseMaterialAttributes(bool bInUseMaterialAttributes) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_SetUseMaterialAttributes(swigCPtr, bInUseMaterialAttributes);
  }

  public bool GetMaterialFunctionOnly() {
    bool ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetMaterialFunctionOnly(swigCPtr);
    return ret;
  }

  public void SetMaterialFunctionOnly(bool bInMaterialFunctionOnly) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_SetMaterialFunctionOnly(swigCPtr, bInMaterialFunctionOnly);
  }

  public float GetOpacityMaskClipValue() {
    float ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetOpacityMaskClipValue(swigCPtr);
    return ret;
  }

  public void SetOpacityMaskClipValue(float InClipValue) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_SetOpacityMaskClipValue(swigCPtr, InClipValue);
  }

  public int GetExpressionsCount() {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetExpressionsCount(swigCPtr);
    return ret;
  }

  public FDatasmithFacadeMaterialExpression GetExpression(int Index) {
	global::System.IntPtr objectPtr = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetExpression(swigCPtr, Index);
	if(objectPtr == global::System.IntPtr.Zero)
	{
		return null;
	}
	else
	{
		//Query the type with a temporary wrapper with no memory ownership.
		EDatasmithFacadeMaterialExpressionType ExpressionType = (new FDatasmithFacadeMaterialExpression(objectPtr, false)).GetExpressionType();

		switch(ExpressionType)
		{
		case EDatasmithFacadeMaterialExpressionType.ConstantBool:
			return new FDatasmithFacadeMaterialExpressionBool(objectPtr, true);
		case EDatasmithFacadeMaterialExpressionType.ConstantColor:
			return new FDatasmithFacadeMaterialExpressionColor(objectPtr, true);
		case EDatasmithFacadeMaterialExpressionType.ConstantScalar:
			return new FDatasmithFacadeMaterialExpressionScalar(objectPtr, true);
		case EDatasmithFacadeMaterialExpressionType.FlattenNormal:
			return new FDatasmithFacadeMaterialExpressionFlattenNormal(objectPtr, true);
		case EDatasmithFacadeMaterialExpressionType.FunctionCall:
			return new FDatasmithFacadeMaterialExpressionFunctionCall(objectPtr, true);
		case EDatasmithFacadeMaterialExpressionType.Generic:
			return new FDatasmithFacadeMaterialExpressionGeneric(objectPtr, true);
		case EDatasmithFacadeMaterialExpressionType.Texture:
			return new FDatasmithFacadeMaterialExpressionTexture(objectPtr, true);
		case EDatasmithFacadeMaterialExpressionType.TextureCoordinate:
			return new FDatasmithFacadeMaterialExpressionTextureCoordinate(objectPtr, true);
		default:
			return null;
		}
	}
}

  public int GetExpressionIndex(FDatasmithFacadeMaterialExpression Expression) {
    int ret = DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetExpressionIndex(swigCPtr, FDatasmithFacadeMaterialExpression.getCPtr(Expression));
    if (DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Pending) throw DatasmithFacadeCSharpPINVOKE.SWIGPendingException.Retrieve();
    return ret;
  }

  public void ResetExpressionGraph() {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_ResetExpressionGraph(swigCPtr);
  }

  public void SetParentLabel(string InParentLabel) {
    DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_SetParentLabel(swigCPtr, InParentLabel);
  }

  public string GetParentLabel() {
    string ret = global::System.Runtime.InteropServices.Marshal.PtrToStringUni(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_GetParentLabel(swigCPtr));
    return ret;
  }

  public FDatasmithFacadeMaterialExpressionBool AddMaterialExpressionBool() {
    FDatasmithFacadeMaterialExpressionBool ret = new FDatasmithFacadeMaterialExpressionBool(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionBool(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeMaterialExpressionColor AddMaterialExpressionColor() {
    FDatasmithFacadeMaterialExpressionColor ret = new FDatasmithFacadeMaterialExpressionColor(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionColor(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeMaterialExpressionFlattenNormal AddMaterialExpressionFlattenNormal() {
    FDatasmithFacadeMaterialExpressionFlattenNormal ret = new FDatasmithFacadeMaterialExpressionFlattenNormal(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionFlattenNormal(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeMaterialExpressionFunctionCall AddMaterialExpressionFunctionCall() {
    FDatasmithFacadeMaterialExpressionFunctionCall ret = new FDatasmithFacadeMaterialExpressionFunctionCall(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionFunctionCall(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeMaterialExpressionGeneric AddMaterialExpressionGeneric() {
    FDatasmithFacadeMaterialExpressionGeneric ret = new FDatasmithFacadeMaterialExpressionGeneric(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionGeneric(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeMaterialExpressionScalar AddMaterialExpressionScalar() {
    FDatasmithFacadeMaterialExpressionScalar ret = new FDatasmithFacadeMaterialExpressionScalar(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionScalar(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeMaterialExpressionTexture AddMaterialExpressionTexture() {
    FDatasmithFacadeMaterialExpressionTexture ret = new FDatasmithFacadeMaterialExpressionTexture(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionTexture(swigCPtr), true);
    return ret;
  }

  public FDatasmithFacadeMaterialExpressionTextureCoordinate AddMaterialExpressionTextureCoordinate() {
    FDatasmithFacadeMaterialExpressionTextureCoordinate ret = new FDatasmithFacadeMaterialExpressionTextureCoordinate(DatasmithFacadeCSharpPINVOKE.FDatasmithFacadeUEPbrMaterial_AddMaterialExpressionTextureCoordinate(swigCPtr), true);
    return ret;
  }

}
