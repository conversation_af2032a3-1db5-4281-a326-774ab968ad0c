<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <EngineSourcePath>..\..\..\..\..\</EngineSourcePath>
    <PrereqPath>$(EngineSourcePath)Programs\PrereqInstaller\</PrereqPath>
    <WixToolPath>$(EngineSourcePath)ThirdParty\WiX\3.8\</WixToolPath>
    <WixTargetsPath>$(WixToolPath)Wix.targets</WixTargetsPath>
    <WixTasksPath>$(WixToolPath)wixtasks.dll</WixTasksPath>
    <SuppressSpecificWarnings>
        1076;1077
    </SuppressSpecificWarnings>
    <DefineConstants>
    </DefineConstants>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>3.10</ProductVersion>
    <ProjectGuid>e3cd0e1d-2bce-4644-a690-6504f4407146</ProjectGuid>
    <SchemaVersion>2.0</SchemaVersion>
    <OutputName>UnrealDatasmithSolidWorksExporter</OutputName>
    <OutputType>Package</OutputType>
    <WixTasksPath>wixtasks.dll</WixTasksPath>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <OutputPath>$(SolutionDir)\bin\$(Configuration)\</OutputPath>
    <IntermediateOutputPath>obj\$(Configuration)\</IntermediateOutputPath>
    <DefineConstants>Debug</DefineConstants>
    <SuppressIces>ICE30;ICE82;ICE03</SuppressIces>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <OutputPath>$(SolutionDir)\bin\$(Configuration)\</OutputPath>
    <IntermediateOutputPath>obj\$(Configuration)\</IntermediateOutputPath>
    <SuppressIces>ICE30;ICE82;ICE03;ICE60;ICE57</SuppressIces>
    <LeaveTemporaryFiles>False</LeaveTemporaryFiles>
    <SuppressPdbOutput>True</SuppressPdbOutput>
    <VerboseOutput>True</VerboseOutput>
    <CompilerAdditionalOptions>-trace</CompilerAdditionalOptions>
    <Cultures>
    </Cultures>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="CustomActions.wxs" />
    <Compile Include="CustomExitDialog.wxs" />
    <Compile Include="CustomInstallDirDlg.wxs" />
    <Compile Include="CustomLicenseAgreementDlg.wxs" />
    <Compile Include="CustomWelcomeDialog.wxs" />
    <Compile Include="CustomWixUI_InstallDir.wxs" />
    <Compile Include="GenericUiBrandDialog.wxs" />
    <Compile Include="Product.wxs" />
  </ItemGroup>
  <ItemGroup>
    <WixExtension Include="WixUtilExtension">
      <HintPath>$(WixToolPath)WixUtilExtension.dll</HintPath>
      <Name>WixUtilExtension</Name>
    </WixExtension>
    <WixExtension Include="WixUIExtension">
      <HintPath>$(WixToolPath)WixUIExtension.dll</HintPath>
      <Name>WixUIExtension</Name>
    </WixExtension>
    <WixExtension Include="WixNetFxExtension">
      <HintPath>$(WixToolPath)WixNetFxExtension.dll</HintPath>
      <Name>WixNetFxExtension</Name>
    </WixExtension>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Localization" />
    <Folder Include="Scripts" />
    <Folder Include="Resources" />
    <Folder Include="Resources\Images" />
    <Folder Include="Resources\Images\Background" />
    <Folder Include="Resources\Images\General" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Scripts\CreateMultilanguageInstaller.bat" />
    <Content Include="Scripts\CreateMultilanguageInstaller.py" />
    <None Include="Product.template" />
    <Content Include="Resources\Banner.bmp" />
    <Content Include="Resources\Dialog.bmp" />
    <Content Include="Resources\Exporter Plugin End User License Agreement.rtf" />
    <Content Include="Resources\Images\Background\28190.bmp" />
    <Content Include="Resources\Images\Background\28402.bmp" />
    <Content Include="Resources\Images\Background\28674.bmp" />
    <Content Include="Resources\Images\Background\30668.bmp" />
    <Content Include="Resources\Images\Background\30725.bmp" />
    <Content Include="Resources\Images\Epic_Games_logo.bmp" />
    <Content Include="Resources\Images\General\banner.bmp" />
    <Content Include="Resources\Images\General\unreal-logo.jpg" />
    <Content Include="Resources\UnrealEngine.ico" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Localization\de-DE.wxl" />
    <EmbeddedResource Include="Localization\en-US.wxl" />
    <EmbeddedResource Include="Localization\es-ES.wxl" />
    <EmbeddedResource Include="Localization\fr-FR.wxl" />
    <EmbeddedResource Include="Localization\ja-JP.wxl" />
    <EmbeddedResource Include="Localization\ko-KR.wxl" />
    <EmbeddedResource Include="Localization\pt-PT.wxl" />
    <EmbeddedResource Include="Localization\UtilExtension_es-es.wxl" />
    <EmbeddedResource Include="Localization\UtilExtension_fr-fr.wxl" />
    <EmbeddedResource Include="Localization\UtilExtension_ja-jp.wxl" />
    <EmbeddedResource Include="Localization\UtilExtension_ko-kr.wxl" />
    <EmbeddedResource Include="Localization\UtilExtension_pt-pt.wxl" />
    <EmbeddedResource Include="Localization\UtilExtension_zh_cn.wxl" />
    <EmbeddedResource Include="Localization\zh-CN.wxl" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RegistryKeyExistsCustomAction\RegistryKeyExistsCustomAction.csproj">
      <Name>RegistryKeyExistsCustomAction</Name>
      <Project>{e623b6ca-6f1e-4db0-8489-a974d625cce4}</Project>
      <Private>True</Private>
      <DoNotHarvest>True</DoNotHarvest>
      <RefProjectOutputGroups>Binaries;Content;Satellites</RefProjectOutputGroups>
      <RefTargetDir>INSTALLFOLDER</RefTargetDir>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(WixTargetsPath)" Condition=" '$(WixTargetsPath)' != '' " />
  <PropertyGroup>
    <PostBuildEvent>call "$(ProjectDir)Scripts\CreateMultilanguageInstaller.bat" $(OutDir)</PostBuildEvent>
  </PropertyGroup>
  <!--
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets" Condition=" '$(WixTargetsPath)' == '' AND Exists('$(MSBuildExtensionsPath32)\Microsoft\WiX\v3.x\Wix.targets') " />
  <Target Name="EnsureWixToolsetInstalled" Condition=" '$(WixTargetsImported)' != 'true' ">
    <Error Text="The WiX Toolset v3.11 (or newer) build tools must be installed to build this project. To download the WiX Toolset, see http://wixtoolset.org/releases/" />
  </Target>
  -->
  <!--
	To modify your build process, add your task inside one of the targets below and uncomment it.
	Other similar extension points exist, see Wix.targets.
	<Target Name="BeforeBuild">
	</Target>
	<Target Name="AfterBuild">
	</Target>
	-->
</Project>