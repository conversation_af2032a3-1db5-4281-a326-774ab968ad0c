// Copyright Epic Games, Inc. All Rights Reserved.
#pragma once

#include "GeometryCollectionTest.h"

namespace GeometryCollectionTest
{	
	
	void BuildProximity();
	
	void GeometryDeleteFromStart();

	void GeometryDeleteFromEnd();

	void GeometryDeleteFromMiddle();

	void GeometryDeleteMultipleFromMiddle();

	void GeometryDeleteRandom();

	void GeometryDeleteRandom2();

	void GeometryDeleteAll();

	void GeometrySwapFlat();

	void TestFracturedGeometry();
	
}
