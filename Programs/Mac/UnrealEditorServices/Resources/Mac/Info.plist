<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>NSAppleEventsUsageDescription</key>
	<string>Please grant permission so that Unreal can run scripts through the terminal.</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>uproject</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string>UProject.icns</string>
			<key>CFBundleTypeName</key>
			<string>Unreal Project</string>
			<key>CFBundleTypeRole</key>
			<string>Viewer</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.epicgames.uproject</string>
			</array>
			<key>LSTypeIsPackage</key>
			<integer>0</integer>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>${EXECUTABLE_NAME}</string>
	<key>CFBundleIconFile</key>
	<string>UnrealEditorServices</string>
	<key>CFBundleIdentifier</key>
	<string>com.epicgames.UnrealEditorServices</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>${EXECUTABLE_NAME}</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.7</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>1.7</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.developer-tools</string>
	<key>LSBackgroundOnly</key>
	<true/>
	<key>LSMinimumSystemVersion</key>
	<string>10.9</string>
	<key>LSUIElement</key>
	<true/>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright Epic Games, Inc. All rights reserved.</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
	<key>NSServices</key>
	<array>
		<dict>
			<key>NSMenuItem</key>
			<dict>
				<key>default</key>
				<string>Launch Game</string>
			</dict>
			<key>NSMessage</key>
			<string>launchGameService</string>
			<key>NSPortName</key>
			<string>${EXECUTABLE_NAME}</string>
			<key>NSRequiredContext</key>
			<dict>
				<key>NSServiceCategory</key>
				<string>public.source-code</string>
				<key>NSTextContent</key>
				<array>
					<string>URL</string>
					<string>FilePath</string>
				</array>
			</dict>
			<key>NSSendFileTypes</key>
			<array>
				<string>com.epicgames.uproject</string>
			</array>
		</dict>
		<dict>
			<key>NSMenuItem</key>
			<dict>
				<key>default</key>
				<string>Generate Xcode Project</string>
			</dict>
			<key>NSMessage</key>
			<string>generateXcodeProjectService</string>
			<key>NSPortName</key>
			<string>${EXECUTABLE_NAME}</string>
			<key>NSRequiredContext</key>
			<dict>
				<key>NSServiceCategory</key>
				<string>public.source-code</string>
				<key>NSTextContent</key>
				<array>
					<string>URL</string>
					<string>FilePath</string>
				</array>
			</dict>
			<key>NSSendFileTypes</key>
			<array>
				<string>com.epicgames.uproject</string>
			</array>
		</dict>
		<dict>
			<key>NSMenuItem</key>
			<dict>
				<key>default</key>
				<string>Switch Unreal Engine Version</string>
			</dict>
			<key>NSMessage</key>
			<string>switchUnrealEngineVersionService</string>
			<key>NSPortName</key>
			<string>${EXECUTABLE_NAME}</string>
			<key>NSRequiredContext</key>
			<dict>
				<key>NSServiceCategory</key>
				<string>public.source-code</string>
				<key>NSTextContent</key>
				<array>
					<string>URL</string>
					<string>FilePath</string>
				</array>
			</dict>
			<key>NSSendFileTypes</key>
			<array>
				<string>com.epicgames.uproject</string>
			</array>
		</dict>
	</array>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.source-code</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Unreal Project</string>
			<key>UTTypeIdentifier</key>
			<string>com.epicgames.uproject</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>uproject</string>
				</array>
			</dict>
		</dict>
	</array>
</dict>
</plist>
