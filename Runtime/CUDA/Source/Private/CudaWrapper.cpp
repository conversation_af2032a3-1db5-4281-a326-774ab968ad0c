// Copyright Epic Games, Inc. All Rights Reserved.
// This file was automatically generated. DO NOT EDIT!
// Generated from cuda.h v11.2.2 sha256:8a2a2402d8e2497e87147b407d2cd15cd51018d050c521bbcd95ed1406203c86

#include "CudaWrapper.h"
#include "HAL/PlatformProcess.h"

void* OpenCudaDriverLibrary()
{
	#ifdef _WIN32
	return FPlatformProcess::GetDllHandle(TEXT("nvcuda.dll"));
	#else
	return FPlatformProcess::GetDllHandle(TEXT("libcuda.so"));
	#endif
}

bool LoadCudaDriverFunctions(void* library, CUDA_DRIVER_API_FUNCTION_LIST* funcList)
{
	if (library == nullptr || funcList == nullptr) {
		return false;
	}
	
	funcList->cuGetErrorString = (PFNCUGETERRORSTRING)(FPlatformProcess::GetDllExport(library, TEXT("cuGetErrorString")));
	funcList->cuGetErrorName = (PFNCUGETERRORNAME)(FPlatformProcess::GetDllExport(library, TEXT("cuGetErrorName")));
	funcList->cuInit = (PFNCUINIT)(FPlatformProcess::GetDllExport(library, TEXT("cuInit")));
	funcList->cuDriverGetVersion = (PFNCUDRIVERGETVERSION)(FPlatformProcess::GetDllExport(library, TEXT("cuDriverGetVersion")));
	funcList->cuDeviceGet = (PFNCUDEVICEGET)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGet")));
	funcList->cuDeviceGetCount = (PFNCUDEVICEGETCOUNT)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetCount")));
	funcList->cuDeviceGetName = (PFNCUDEVICEGETNAME)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetName")));
	funcList->cuDeviceGetUuid = (PFNCUDEVICEGETUUID)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetUuid")));
	funcList->cuDeviceGetLuid = (PFNCUDEVICEGETLUID)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetLuid")));
	funcList->cuDeviceTotalMem = (PFNCUDEVICETOTALMEM)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceTotalMem_v2")));
	funcList->cuDeviceGetTexture1DLinearMaxWidth = (PFNCUDEVICEGETTEXTURE1DLINEARMAXWIDTH)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetTexture1DLinearMaxWidth")));
	funcList->cuDeviceGetAttribute = (PFNCUDEVICEGETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetAttribute")));
	funcList->cuDeviceGetNvSciSyncAttributes = (PFNCUDEVICEGETNVSCISYNCATTRIBUTES)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetNvSciSyncAttributes")));
	funcList->cuDeviceSetMemPool = (PFNCUDEVICESETMEMPOOL)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceSetMemPool")));
	funcList->cuDeviceGetMemPool = (PFNCUDEVICEGETMEMPOOL)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetMemPool")));
	funcList->cuDeviceGetDefaultMemPool = (PFNCUDEVICEGETDEFAULTMEMPOOL)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetDefaultMemPool")));
	funcList->cuDeviceGetProperties = (PFNCUDEVICEGETPROPERTIES)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetProperties")));
	funcList->cuDeviceComputeCapability = (PFNCUDEVICECOMPUTECAPABILITY)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceComputeCapability")));
	funcList->cuDevicePrimaryCtxRetain = (PFNCUDEVICEPRIMARYCTXRETAIN)(FPlatformProcess::GetDllExport(library, TEXT("cuDevicePrimaryCtxRetain")));
	funcList->cuDevicePrimaryCtxRelease = (PFNCUDEVICEPRIMARYCTXRELEASE)(FPlatformProcess::GetDllExport(library, TEXT("cuDevicePrimaryCtxRelease_v2")));
	funcList->cuDevicePrimaryCtxSetFlags = (PFNCUDEVICEPRIMARYCTXSETFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuDevicePrimaryCtxSetFlags_v2")));
	funcList->cuDevicePrimaryCtxGetState = (PFNCUDEVICEPRIMARYCTXGETSTATE)(FPlatformProcess::GetDllExport(library, TEXT("cuDevicePrimaryCtxGetState")));
	funcList->cuDevicePrimaryCtxReset = (PFNCUDEVICEPRIMARYCTXRESET)(FPlatformProcess::GetDllExport(library, TEXT("cuDevicePrimaryCtxReset_v2")));
	funcList->cuCtxCreate = (PFNCUCTXCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxCreate_v2")));
	funcList->cuCtxDestroy = (PFNCUCTXDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxDestroy_v2")));
	funcList->cuCtxPushCurrent = (PFNCUCTXPUSHCURRENT)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxPushCurrent_v2")));
	funcList->cuCtxPopCurrent = (PFNCUCTXPOPCURRENT)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxPopCurrent_v2")));
	funcList->cuCtxSetCurrent = (PFNCUCTXSETCURRENT)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxSetCurrent")));
	funcList->cuCtxGetCurrent = (PFNCUCTXGETCURRENT)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxGetCurrent")));
	funcList->cuCtxGetDevice = (PFNCUCTXGETDEVICE)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxGetDevice")));
	funcList->cuCtxGetFlags = (PFNCUCTXGETFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxGetFlags")));
	funcList->cuCtxSynchronize = (PFNCUCTXSYNCHRONIZE)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxSynchronize")));
	funcList->cuCtxSetLimit = (PFNCUCTXSETLIMIT)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxSetLimit")));
	funcList->cuCtxGetLimit = (PFNCUCTXGETLIMIT)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxGetLimit")));
	funcList->cuCtxGetCacheConfig = (PFNCUCTXGETCACHECONFIG)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxGetCacheConfig")));
	funcList->cuCtxSetCacheConfig = (PFNCUCTXSETCACHECONFIG)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxSetCacheConfig")));
	funcList->cuCtxGetSharedMemConfig = (PFNCUCTXGETSHAREDMEMCONFIG)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxGetSharedMemConfig")));
	funcList->cuCtxSetSharedMemConfig = (PFNCUCTXSETSHAREDMEMCONFIG)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxSetSharedMemConfig")));
	funcList->cuCtxGetApiVersion = (PFNCUCTXGETAPIVERSION)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxGetApiVersion")));
	funcList->cuCtxGetStreamPriorityRange = (PFNCUCTXGETSTREAMPRIORITYRANGE)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxGetStreamPriorityRange")));
	funcList->cuCtxResetPersistingL2Cache = (PFNCUCTXRESETPERSISTINGL2CACHE)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxResetPersistingL2Cache")));
	funcList->cuCtxAttach = (PFNCUCTXATTACH)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxAttach")));
	funcList->cuCtxDetach = (PFNCUCTXDETACH)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxDetach")));
	funcList->cuModuleLoad = (PFNCUMODULELOAD)(FPlatformProcess::GetDllExport(library, TEXT("cuModuleLoad")));
	funcList->cuModuleLoadData = (PFNCUMODULELOADDATA)(FPlatformProcess::GetDllExport(library, TEXT("cuModuleLoadData")));
	funcList->cuModuleLoadDataEx = (PFNCUMODULELOADDATAEX)(FPlatformProcess::GetDllExport(library, TEXT("cuModuleLoadDataEx")));
	funcList->cuModuleLoadFatBinary = (PFNCUMODULELOADFATBINARY)(FPlatformProcess::GetDllExport(library, TEXT("cuModuleLoadFatBinary")));
	funcList->cuModuleUnload = (PFNCUMODULEUNLOAD)(FPlatformProcess::GetDllExport(library, TEXT("cuModuleUnload")));
	funcList->cuModuleGetFunction = (PFNCUMODULEGETFUNCTION)(FPlatformProcess::GetDllExport(library, TEXT("cuModuleGetFunction")));
	funcList->cuModuleGetGlobal = (PFNCUMODULEGETGLOBAL)(FPlatformProcess::GetDllExport(library, TEXT("cuModuleGetGlobal_v2")));
	funcList->cuModuleGetTexRef = (PFNCUMODULEGETTEXREF)(FPlatformProcess::GetDllExport(library, TEXT("cuModuleGetTexRef")));
	funcList->cuModuleGetSurfRef = (PFNCUMODULEGETSURFREF)(FPlatformProcess::GetDllExport(library, TEXT("cuModuleGetSurfRef")));
	funcList->cuLinkCreate = (PFNCULINKCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuLinkCreate_v2")));
	funcList->cuLinkAddData = (PFNCULINKADDDATA)(FPlatformProcess::GetDllExport(library, TEXT("cuLinkAddData_v2")));
	funcList->cuLinkAddFile = (PFNCULINKADDFILE)(FPlatformProcess::GetDllExport(library, TEXT("cuLinkAddFile_v2")));
	funcList->cuLinkComplete = (PFNCULINKCOMPLETE)(FPlatformProcess::GetDllExport(library, TEXT("cuLinkComplete")));
	funcList->cuLinkDestroy = (PFNCULINKDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuLinkDestroy")));
	funcList->cuMemGetInfo = (PFNCUMEMGETINFO)(FPlatformProcess::GetDllExport(library, TEXT("cuMemGetInfo_v2")));
	funcList->cuMemAlloc = (PFNCUMEMALLOC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemAlloc_v2")));
	funcList->cuMemAllocPitch = (PFNCUMEMALLOCPITCH)(FPlatformProcess::GetDllExport(library, TEXT("cuMemAllocPitch_v2")));
	funcList->cuMemFree = (PFNCUMEMFREE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemFree_v2")));
	funcList->cuMemGetAddressRange = (PFNCUMEMGETADDRESSRANGE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemGetAddressRange_v2")));
	funcList->cuMemAllocHost = (PFNCUMEMALLOCHOST)(FPlatformProcess::GetDllExport(library, TEXT("cuMemAllocHost_v2")));
	funcList->cuMemFreeHost = (PFNCUMEMFREEHOST)(FPlatformProcess::GetDllExport(library, TEXT("cuMemFreeHost")));
	funcList->cuMemHostAlloc = (PFNCUMEMHOSTALLOC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemHostAlloc")));
	funcList->cuMemHostGetDevicePointer = (PFNCUMEMHOSTGETDEVICEPOINTER)(FPlatformProcess::GetDllExport(library, TEXT("cuMemHostGetDevicePointer_v2")));
	funcList->cuMemHostGetFlags = (PFNCUMEMHOSTGETFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuMemHostGetFlags")));
	funcList->cuMemAllocManaged = (PFNCUMEMALLOCMANAGED)(FPlatformProcess::GetDllExport(library, TEXT("cuMemAllocManaged")));
	funcList->cuDeviceGetByPCIBusId = (PFNCUDEVICEGETBYPCIBUSID)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetByPCIBusId")));
	funcList->cuDeviceGetPCIBusId = (PFNCUDEVICEGETPCIBUSID)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetPCIBusId")));
	funcList->cuIpcGetEventHandle = (PFNCUIPCGETEVENTHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuIpcGetEventHandle")));
	funcList->cuIpcOpenEventHandle = (PFNCUIPCOPENEVENTHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuIpcOpenEventHandle")));
	funcList->cuIpcGetMemHandle = (PFNCUIPCGETMEMHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuIpcGetMemHandle")));
	funcList->cuIpcOpenMemHandle = (PFNCUIPCOPENMEMHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuIpcOpenMemHandle_v2")));
	funcList->cuIpcCloseMemHandle = (PFNCUIPCCLOSEMEMHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuIpcCloseMemHandle")));
	funcList->cuMemHostRegister = (PFNCUMEMHOSTREGISTER)(FPlatformProcess::GetDllExport(library, TEXT("cuMemHostRegister_v2")));
	funcList->cuMemHostUnregister = (PFNCUMEMHOSTUNREGISTER)(FPlatformProcess::GetDllExport(library, TEXT("cuMemHostUnregister")));
	funcList->cuMemcpy = (PFNCUMEMCPY)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpy")));
	funcList->cuMemcpyPeer = (PFNCUMEMCPYPEER)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyPeer")));
	funcList->cuMemcpyHtoD = (PFNCUMEMCPYHTOD)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyHtoD_v2")));
	funcList->cuMemcpyDtoH = (PFNCUMEMCPYDTOH)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyDtoH_v2")));
	funcList->cuMemcpyDtoD = (PFNCUMEMCPYDTOD)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyDtoD_v2")));
	funcList->cuMemcpyDtoA = (PFNCUMEMCPYDTOA)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyDtoA_v2")));
	funcList->cuMemcpyAtoD = (PFNCUMEMCPYATOD)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyAtoD_v2")));
	funcList->cuMemcpyHtoA = (PFNCUMEMCPYHTOA)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyHtoA_v2")));
	funcList->cuMemcpyAtoH = (PFNCUMEMCPYATOH)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyAtoH_v2")));
	funcList->cuMemcpyAtoA = (PFNCUMEMCPYATOA)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyAtoA_v2")));
	funcList->cuMemcpy2D = (PFNCUMEMCPY2D)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpy2D_v2")));
	funcList->cuMemcpy2DUnaligned = (PFNCUMEMCPY2DUNALIGNED)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpy2DUnaligned_v2")));
	funcList->cuMemcpy3D = (PFNCUMEMCPY3D)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpy3D_v2")));
	funcList->cuMemcpy3DPeer = (PFNCUMEMCPY3DPEER)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpy3DPeer")));
	funcList->cuMemcpyAsync = (PFNCUMEMCPYASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyAsync")));
	funcList->cuMemcpyPeerAsync = (PFNCUMEMCPYPEERASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyPeerAsync")));
	funcList->cuMemcpyHtoDAsync = (PFNCUMEMCPYHTODASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyHtoDAsync_v2")));
	funcList->cuMemcpyDtoHAsync = (PFNCUMEMCPYDTOHASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyDtoHAsync_v2")));
	funcList->cuMemcpyDtoDAsync = (PFNCUMEMCPYDTODASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyDtoDAsync_v2")));
	funcList->cuMemcpyHtoAAsync = (PFNCUMEMCPYHTOAASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyHtoAAsync_v2")));
	funcList->cuMemcpyAtoHAsync = (PFNCUMEMCPYATOHASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpyAtoHAsync_v2")));
	funcList->cuMemcpy2DAsync = (PFNCUMEMCPY2DASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpy2DAsync_v2")));
	funcList->cuMemcpy3DAsync = (PFNCUMEMCPY3DASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpy3DAsync_v2")));
	funcList->cuMemcpy3DPeerAsync = (PFNCUMEMCPY3DPEERASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemcpy3DPeerAsync")));
	funcList->cuMemsetD8 = (PFNCUMEMSETD8)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD8_v2")));
	funcList->cuMemsetD16 = (PFNCUMEMSETD16)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD16_v2")));
	funcList->cuMemsetD32 = (PFNCUMEMSETD32)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD32_v2")));
	funcList->cuMemsetD2D8 = (PFNCUMEMSETD2D8)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD2D8_v2")));
	funcList->cuMemsetD2D16 = (PFNCUMEMSETD2D16)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD2D16_v2")));
	funcList->cuMemsetD2D32 = (PFNCUMEMSETD2D32)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD2D32_v2")));
	funcList->cuMemsetD8Async = (PFNCUMEMSETD8ASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD8Async")));
	funcList->cuMemsetD16Async = (PFNCUMEMSETD16ASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD16Async")));
	funcList->cuMemsetD32Async = (PFNCUMEMSETD32ASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD32Async")));
	funcList->cuMemsetD2D8Async = (PFNCUMEMSETD2D8ASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD2D8Async")));
	funcList->cuMemsetD2D16Async = (PFNCUMEMSETD2D16ASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD2D16Async")));
	funcList->cuMemsetD2D32Async = (PFNCUMEMSETD2D32ASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemsetD2D32Async")));
	funcList->cuArrayCreate = (PFNCUARRAYCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuArrayCreate_v2")));
	funcList->cuArrayGetDescriptor = (PFNCUARRAYGETDESCRIPTOR)(FPlatformProcess::GetDllExport(library, TEXT("cuArrayGetDescriptor_v2")));
	funcList->cuArrayGetSparseProperties = (PFNCUARRAYGETSPARSEPROPERTIES)(FPlatformProcess::GetDllExport(library, TEXT("cuArrayGetSparseProperties")));
	funcList->cuMipmappedArrayGetSparseProperties = (PFNCUMIPMAPPEDARRAYGETSPARSEPROPERTIES)(FPlatformProcess::GetDllExport(library, TEXT("cuMipmappedArrayGetSparseProperties")));
	funcList->cuArrayGetPlane = (PFNCUARRAYGETPLANE)(FPlatformProcess::GetDllExport(library, TEXT("cuArrayGetPlane")));
	funcList->cuArrayDestroy = (PFNCUARRAYDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuArrayDestroy")));
	funcList->cuArray3DCreate = (PFNCUARRAY3DCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuArray3DCreate_v2")));
	funcList->cuArray3DGetDescriptor = (PFNCUARRAY3DGETDESCRIPTOR)(FPlatformProcess::GetDllExport(library, TEXT("cuArray3DGetDescriptor_v2")));
	funcList->cuMipmappedArrayCreate = (PFNCUMIPMAPPEDARRAYCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuMipmappedArrayCreate")));
	funcList->cuMipmappedArrayGetLevel = (PFNCUMIPMAPPEDARRAYGETLEVEL)(FPlatformProcess::GetDllExport(library, TEXT("cuMipmappedArrayGetLevel")));
	funcList->cuMipmappedArrayDestroy = (PFNCUMIPMAPPEDARRAYDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuMipmappedArrayDestroy")));
	funcList->cuMemAddressReserve = (PFNCUMEMADDRESSRESERVE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemAddressReserve")));
	funcList->cuMemAddressFree = (PFNCUMEMADDRESSFREE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemAddressFree")));
	funcList->cuMemCreate = (PFNCUMEMCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemCreate")));
	funcList->cuMemRelease = (PFNCUMEMRELEASE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemRelease")));
	funcList->cuMemMap = (PFNCUMEMMAP)(FPlatformProcess::GetDllExport(library, TEXT("cuMemMap")));
	funcList->cuMemMapArrayAsync = (PFNCUMEMMAPARRAYASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemMapArrayAsync")));
	funcList->cuMemUnmap = (PFNCUMEMUNMAP)(FPlatformProcess::GetDllExport(library, TEXT("cuMemUnmap")));
	funcList->cuMemSetAccess = (PFNCUMEMSETACCESS)(FPlatformProcess::GetDllExport(library, TEXT("cuMemSetAccess")));
	funcList->cuMemGetAccess = (PFNCUMEMGETACCESS)(FPlatformProcess::GetDllExport(library, TEXT("cuMemGetAccess")));
	funcList->cuMemExportToShareableHandle = (PFNCUMEMEXPORTTOSHAREABLEHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemExportToShareableHandle")));
	funcList->cuMemImportFromShareableHandle = (PFNCUMEMIMPORTFROMSHAREABLEHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemImportFromShareableHandle")));
	funcList->cuMemGetAllocationGranularity = (PFNCUMEMGETALLOCATIONGRANULARITY)(FPlatformProcess::GetDllExport(library, TEXT("cuMemGetAllocationGranularity")));
	funcList->cuMemGetAllocationPropertiesFromHandle = (PFNCUMEMGETALLOCATIONPROPERTIESFROMHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemGetAllocationPropertiesFromHandle")));
	funcList->cuMemRetainAllocationHandle = (PFNCUMEMRETAINALLOCATIONHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemRetainAllocationHandle")));
	funcList->cuMemFreeAsync = (PFNCUMEMFREEASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemFreeAsync")));
	funcList->cuMemAllocAsync = (PFNCUMEMALLOCASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemAllocAsync")));
	funcList->cuMemPoolTrimTo = (PFNCUMEMPOOLTRIMTO)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolTrimTo")));
	funcList->cuMemPoolSetAttribute = (PFNCUMEMPOOLSETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolSetAttribute")));
	funcList->cuMemPoolGetAttribute = (PFNCUMEMPOOLGETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolGetAttribute")));
	funcList->cuMemPoolSetAccess = (PFNCUMEMPOOLSETACCESS)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolSetAccess")));
	funcList->cuMemPoolGetAccess = (PFNCUMEMPOOLGETACCESS)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolGetAccess")));
	funcList->cuMemPoolCreate = (PFNCUMEMPOOLCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolCreate")));
	funcList->cuMemPoolDestroy = (PFNCUMEMPOOLDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolDestroy")));
	funcList->cuMemAllocFromPoolAsync = (PFNCUMEMALLOCFROMPOOLASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemAllocFromPoolAsync")));
	funcList->cuMemPoolExportToShareableHandle = (PFNCUMEMPOOLEXPORTTOSHAREABLEHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolExportToShareableHandle")));
	funcList->cuMemPoolImportFromShareableHandle = (PFNCUMEMPOOLIMPORTFROMSHAREABLEHANDLE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolImportFromShareableHandle")));
	funcList->cuMemPoolExportPointer = (PFNCUMEMPOOLEXPORTPOINTER)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolExportPointer")));
	funcList->cuMemPoolImportPointer = (PFNCUMEMPOOLIMPORTPOINTER)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPoolImportPointer")));
	funcList->cuPointerGetAttribute = (PFNCUPOINTERGETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuPointerGetAttribute")));
	funcList->cuMemPrefetchAsync = (PFNCUMEMPREFETCHASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuMemPrefetchAsync")));
	funcList->cuMemAdvise = (PFNCUMEMADVISE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemAdvise")));
	funcList->cuMemRangeGetAttribute = (PFNCUMEMRANGEGETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuMemRangeGetAttribute")));
	funcList->cuMemRangeGetAttributes = (PFNCUMEMRANGEGETATTRIBUTES)(FPlatformProcess::GetDllExport(library, TEXT("cuMemRangeGetAttributes")));
	funcList->cuPointerSetAttribute = (PFNCUPOINTERSETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuPointerSetAttribute")));
	funcList->cuPointerGetAttributes = (PFNCUPOINTERGETATTRIBUTES)(FPlatformProcess::GetDllExport(library, TEXT("cuPointerGetAttributes")));
	funcList->cuStreamCreate = (PFNCUSTREAMCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamCreate")));
	funcList->cuStreamCreateWithPriority = (PFNCUSTREAMCREATEWITHPRIORITY)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamCreateWithPriority")));
	funcList->cuStreamGetPriority = (PFNCUSTREAMGETPRIORITY)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamGetPriority")));
	funcList->cuStreamGetFlags = (PFNCUSTREAMGETFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamGetFlags")));
	funcList->cuStreamGetCtx = (PFNCUSTREAMGETCTX)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamGetCtx")));
	funcList->cuStreamWaitEvent = (PFNCUSTREAMWAITEVENT)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamWaitEvent")));
	funcList->cuStreamAddCallback = (PFNCUSTREAMADDCALLBACK)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamAddCallback")));
	funcList->cuStreamBeginCapture = (PFNCUSTREAMBEGINCAPTURE)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamBeginCapture_v2")));
	funcList->cuThreadExchangeStreamCaptureMode = (PFNCUTHREADEXCHANGESTREAMCAPTUREMODE)(FPlatformProcess::GetDllExport(library, TEXT("cuThreadExchangeStreamCaptureMode")));
	funcList->cuStreamEndCapture = (PFNCUSTREAMENDCAPTURE)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamEndCapture")));
	funcList->cuStreamIsCapturing = (PFNCUSTREAMISCAPTURING)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamIsCapturing")));
	funcList->cuStreamGetCaptureInfo = (PFNCUSTREAMGETCAPTUREINFO)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamGetCaptureInfo")));
	funcList->cuStreamAttachMemAsync = (PFNCUSTREAMATTACHMEMASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamAttachMemAsync")));
	funcList->cuStreamQuery = (PFNCUSTREAMQUERY)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamQuery")));
	funcList->cuStreamSynchronize = (PFNCUSTREAMSYNCHRONIZE)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamSynchronize")));
	funcList->cuStreamDestroy = (PFNCUSTREAMDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamDestroy_v2")));
	funcList->cuStreamCopyAttributes = (PFNCUSTREAMCOPYATTRIBUTES)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamCopyAttributes")));
	funcList->cuStreamGetAttribute = (PFNCUSTREAMGETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamGetAttribute")));
	funcList->cuStreamSetAttribute = (PFNCUSTREAMSETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamSetAttribute")));
	funcList->cuEventCreate = (PFNCUEVENTCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuEventCreate")));
	funcList->cuEventRecord = (PFNCUEVENTRECORD)(FPlatformProcess::GetDllExport(library, TEXT("cuEventRecord")));
	funcList->cuEventRecordWithFlags = (PFNCUEVENTRECORDWITHFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuEventRecordWithFlags")));
	funcList->cuEventQuery = (PFNCUEVENTQUERY)(FPlatformProcess::GetDllExport(library, TEXT("cuEventQuery")));
	funcList->cuEventSynchronize = (PFNCUEVENTSYNCHRONIZE)(FPlatformProcess::GetDllExport(library, TEXT("cuEventSynchronize")));
	funcList->cuEventDestroy = (PFNCUEVENTDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuEventDestroy_v2")));
	funcList->cuEventElapsedTime = (PFNCUEVENTELAPSEDTIME)(FPlatformProcess::GetDllExport(library, TEXT("cuEventElapsedTime")));
	funcList->cuImportExternalMemory = (PFNCUIMPORTEXTERNALMEMORY)(FPlatformProcess::GetDllExport(library, TEXT("cuImportExternalMemory")));
	funcList->cuExternalMemoryGetMappedBuffer = (PFNCUEXTERNALMEMORYGETMAPPEDBUFFER)(FPlatformProcess::GetDllExport(library, TEXT("cuExternalMemoryGetMappedBuffer")));
	funcList->cuExternalMemoryGetMappedMipmappedArray = (PFNCUEXTERNALMEMORYGETMAPPEDMIPMAPPEDARRAY)(FPlatformProcess::GetDllExport(library, TEXT("cuExternalMemoryGetMappedMipmappedArray")));
	funcList->cuDestroyExternalMemory = (PFNCUDESTROYEXTERNALMEMORY)(FPlatformProcess::GetDllExport(library, TEXT("cuDestroyExternalMemory")));
	funcList->cuImportExternalSemaphore = (PFNCUIMPORTEXTERNALSEMAPHORE)(FPlatformProcess::GetDllExport(library, TEXT("cuImportExternalSemaphore")));
	funcList->cuSignalExternalSemaphoresAsync = (PFNCUSIGNALEXTERNALSEMAPHORESASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuSignalExternalSemaphoresAsync")));
	funcList->cuWaitExternalSemaphoresAsync = (PFNCUWAITEXTERNALSEMAPHORESASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuWaitExternalSemaphoresAsync")));
	funcList->cuDestroyExternalSemaphore = (PFNCUDESTROYEXTERNALSEMAPHORE)(FPlatformProcess::GetDllExport(library, TEXT("cuDestroyExternalSemaphore")));
	funcList->cuStreamWaitValue32 = (PFNCUSTREAMWAITVALUE32)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamWaitValue32")));
	funcList->cuStreamWaitValue64 = (PFNCUSTREAMWAITVALUE64)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamWaitValue64")));
	funcList->cuStreamWriteValue32 = (PFNCUSTREAMWRITEVALUE32)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamWriteValue32")));
	funcList->cuStreamWriteValue64 = (PFNCUSTREAMWRITEVALUE64)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamWriteValue64")));
	funcList->cuStreamBatchMemOp = (PFNCUSTREAMBATCHMEMOP)(FPlatformProcess::GetDllExport(library, TEXT("cuStreamBatchMemOp")));
	funcList->cuFuncGetAttribute = (PFNCUFUNCGETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuFuncGetAttribute")));
	funcList->cuFuncSetAttribute = (PFNCUFUNCSETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuFuncSetAttribute")));
	funcList->cuFuncSetCacheConfig = (PFNCUFUNCSETCACHECONFIG)(FPlatformProcess::GetDllExport(library, TEXT("cuFuncSetCacheConfig")));
	funcList->cuFuncSetSharedMemConfig = (PFNCUFUNCSETSHAREDMEMCONFIG)(FPlatformProcess::GetDllExport(library, TEXT("cuFuncSetSharedMemConfig")));
	funcList->cuLaunchKernel = (PFNCULAUNCHKERNEL)(FPlatformProcess::GetDllExport(library, TEXT("cuLaunchKernel")));
	funcList->cuLaunchCooperativeKernel = (PFNCULAUNCHCOOPERATIVEKERNEL)(FPlatformProcess::GetDllExport(library, TEXT("cuLaunchCooperativeKernel")));
	funcList->cuLaunchCooperativeKernelMultiDevice = (PFNCULAUNCHCOOPERATIVEKERNELMULTIDEVICE)(FPlatformProcess::GetDllExport(library, TEXT("cuLaunchCooperativeKernelMultiDevice")));
	funcList->cuLaunchHostFunc = (PFNCULAUNCHHOSTFUNC)(FPlatformProcess::GetDllExport(library, TEXT("cuLaunchHostFunc")));
	funcList->cuFuncSetBlockShape = (PFNCUFUNCSETBLOCKSHAPE)(FPlatformProcess::GetDllExport(library, TEXT("cuFuncSetBlockShape")));
	funcList->cuFuncSetSharedSize = (PFNCUFUNCSETSHAREDSIZE)(FPlatformProcess::GetDllExport(library, TEXT("cuFuncSetSharedSize")));
	funcList->cuParamSetSize = (PFNCUPARAMSETSIZE)(FPlatformProcess::GetDllExport(library, TEXT("cuParamSetSize")));
	funcList->cuParamSeti = (PFNCUPARAMSETI)(FPlatformProcess::GetDllExport(library, TEXT("cuParamSeti")));
	funcList->cuParamSetf = (PFNCUPARAMSETF)(FPlatformProcess::GetDllExport(library, TEXT("cuParamSetf")));
	funcList->cuParamSetv = (PFNCUPARAMSETV)(FPlatformProcess::GetDllExport(library, TEXT("cuParamSetv")));
	funcList->cuLaunch = (PFNCULAUNCH)(FPlatformProcess::GetDllExport(library, TEXT("cuLaunch")));
	funcList->cuLaunchGrid = (PFNCULAUNCHGRID)(FPlatformProcess::GetDllExport(library, TEXT("cuLaunchGrid")));
	funcList->cuLaunchGridAsync = (PFNCULAUNCHGRIDASYNC)(FPlatformProcess::GetDllExport(library, TEXT("cuLaunchGridAsync")));
	funcList->cuParamSetTexRef = (PFNCUPARAMSETTEXREF)(FPlatformProcess::GetDllExport(library, TEXT("cuParamSetTexRef")));
	funcList->cuGraphCreate = (PFNCUGRAPHCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphCreate")));
	funcList->cuGraphAddKernelNode = (PFNCUGRAPHADDKERNELNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddKernelNode")));
	funcList->cuGraphKernelNodeGetParams = (PFNCUGRAPHKERNELNODEGETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphKernelNodeGetParams")));
	funcList->cuGraphKernelNodeSetParams = (PFNCUGRAPHKERNELNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphKernelNodeSetParams")));
	funcList->cuGraphAddMemcpyNode = (PFNCUGRAPHADDMEMCPYNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddMemcpyNode")));
	funcList->cuGraphMemcpyNodeGetParams = (PFNCUGRAPHMEMCPYNODEGETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphMemcpyNodeGetParams")));
	funcList->cuGraphMemcpyNodeSetParams = (PFNCUGRAPHMEMCPYNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphMemcpyNodeSetParams")));
	funcList->cuGraphAddMemsetNode = (PFNCUGRAPHADDMEMSETNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddMemsetNode")));
	funcList->cuGraphMemsetNodeGetParams = (PFNCUGRAPHMEMSETNODEGETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphMemsetNodeGetParams")));
	funcList->cuGraphMemsetNodeSetParams = (PFNCUGRAPHMEMSETNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphMemsetNodeSetParams")));
	funcList->cuGraphAddHostNode = (PFNCUGRAPHADDHOSTNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddHostNode")));
	funcList->cuGraphHostNodeGetParams = (PFNCUGRAPHHOSTNODEGETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphHostNodeGetParams")));
	funcList->cuGraphHostNodeSetParams = (PFNCUGRAPHHOSTNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphHostNodeSetParams")));
	funcList->cuGraphAddChildGraphNode = (PFNCUGRAPHADDCHILDGRAPHNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddChildGraphNode")));
	funcList->cuGraphChildGraphNodeGetGraph = (PFNCUGRAPHCHILDGRAPHNODEGETGRAPH)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphChildGraphNodeGetGraph")));
	funcList->cuGraphAddEmptyNode = (PFNCUGRAPHADDEMPTYNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddEmptyNode")));
	funcList->cuGraphAddEventRecordNode = (PFNCUGRAPHADDEVENTRECORDNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddEventRecordNode")));
	funcList->cuGraphEventRecordNodeGetEvent = (PFNCUGRAPHEVENTRECORDNODEGETEVENT)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphEventRecordNodeGetEvent")));
	funcList->cuGraphEventRecordNodeSetEvent = (PFNCUGRAPHEVENTRECORDNODESETEVENT)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphEventRecordNodeSetEvent")));
	funcList->cuGraphAddEventWaitNode = (PFNCUGRAPHADDEVENTWAITNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddEventWaitNode")));
	funcList->cuGraphEventWaitNodeGetEvent = (PFNCUGRAPHEVENTWAITNODEGETEVENT)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphEventWaitNodeGetEvent")));
	funcList->cuGraphEventWaitNodeSetEvent = (PFNCUGRAPHEVENTWAITNODESETEVENT)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphEventWaitNodeSetEvent")));
	funcList->cuGraphAddExternalSemaphoresSignalNode = (PFNCUGRAPHADDEXTERNALSEMAPHORESSIGNALNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddExternalSemaphoresSignalNode")));
	funcList->cuGraphExternalSemaphoresSignalNodeGetParams = (PFNCUGRAPHEXTERNALSEMAPHORESSIGNALNODEGETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExternalSemaphoresSignalNodeGetParams")));
	funcList->cuGraphExternalSemaphoresSignalNodeSetParams = (PFNCUGRAPHEXTERNALSEMAPHORESSIGNALNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExternalSemaphoresSignalNodeSetParams")));
	funcList->cuGraphAddExternalSemaphoresWaitNode = (PFNCUGRAPHADDEXTERNALSEMAPHORESWAITNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddExternalSemaphoresWaitNode")));
	funcList->cuGraphExternalSemaphoresWaitNodeGetParams = (PFNCUGRAPHEXTERNALSEMAPHORESWAITNODEGETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExternalSemaphoresWaitNodeGetParams")));
	funcList->cuGraphExternalSemaphoresWaitNodeSetParams = (PFNCUGRAPHEXTERNALSEMAPHORESWAITNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExternalSemaphoresWaitNodeSetParams")));
	funcList->cuGraphClone = (PFNCUGRAPHCLONE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphClone")));
	funcList->cuGraphNodeFindInClone = (PFNCUGRAPHNODEFINDINCLONE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphNodeFindInClone")));
	funcList->cuGraphNodeGetType = (PFNCUGRAPHNODEGETTYPE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphNodeGetType")));
	funcList->cuGraphGetNodes = (PFNCUGRAPHGETNODES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphGetNodes")));
	funcList->cuGraphGetRootNodes = (PFNCUGRAPHGETROOTNODES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphGetRootNodes")));
	funcList->cuGraphGetEdges = (PFNCUGRAPHGETEDGES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphGetEdges")));
	funcList->cuGraphNodeGetDependencies = (PFNCUGRAPHNODEGETDEPENDENCIES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphNodeGetDependencies")));
	funcList->cuGraphNodeGetDependentNodes = (PFNCUGRAPHNODEGETDEPENDENTNODES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphNodeGetDependentNodes")));
	funcList->cuGraphAddDependencies = (PFNCUGRAPHADDDEPENDENCIES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphAddDependencies")));
	funcList->cuGraphRemoveDependencies = (PFNCUGRAPHREMOVEDEPENDENCIES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphRemoveDependencies")));
	funcList->cuGraphDestroyNode = (PFNCUGRAPHDESTROYNODE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphDestroyNode")));
	funcList->cuGraphInstantiate = (PFNCUGRAPHINSTANTIATE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphInstantiate_v2")));
	funcList->cuGraphExecKernelNodeSetParams = (PFNCUGRAPHEXECKERNELNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecKernelNodeSetParams")));
	funcList->cuGraphExecMemcpyNodeSetParams = (PFNCUGRAPHEXECMEMCPYNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecMemcpyNodeSetParams")));
	funcList->cuGraphExecMemsetNodeSetParams = (PFNCUGRAPHEXECMEMSETNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecMemsetNodeSetParams")));
	funcList->cuGraphExecHostNodeSetParams = (PFNCUGRAPHEXECHOSTNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecHostNodeSetParams")));
	funcList->cuGraphExecChildGraphNodeSetParams = (PFNCUGRAPHEXECCHILDGRAPHNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecChildGraphNodeSetParams")));
	funcList->cuGraphExecEventRecordNodeSetEvent = (PFNCUGRAPHEXECEVENTRECORDNODESETEVENT)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecEventRecordNodeSetEvent")));
	funcList->cuGraphExecEventWaitNodeSetEvent = (PFNCUGRAPHEXECEVENTWAITNODESETEVENT)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecEventWaitNodeSetEvent")));
	funcList->cuGraphExecExternalSemaphoresSignalNodeSetParams = (PFNCUGRAPHEXECEXTERNALSEMAPHORESSIGNALNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecExternalSemaphoresSignalNodeSetParams")));
	funcList->cuGraphExecExternalSemaphoresWaitNodeSetParams = (PFNCUGRAPHEXECEXTERNALSEMAPHORESWAITNODESETPARAMS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecExternalSemaphoresWaitNodeSetParams")));
	funcList->cuGraphUpload = (PFNCUGRAPHUPLOAD)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphUpload")));
	funcList->cuGraphLaunch = (PFNCUGRAPHLAUNCH)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphLaunch")));
	funcList->cuGraphExecDestroy = (PFNCUGRAPHEXECDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecDestroy")));
	funcList->cuGraphDestroy = (PFNCUGRAPHDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphDestroy")));
	funcList->cuGraphExecUpdate = (PFNCUGRAPHEXECUPDATE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphExecUpdate")));
	funcList->cuGraphKernelNodeCopyAttributes = (PFNCUGRAPHKERNELNODECOPYATTRIBUTES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphKernelNodeCopyAttributes")));
	funcList->cuGraphKernelNodeGetAttribute = (PFNCUGRAPHKERNELNODEGETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphKernelNodeGetAttribute")));
	funcList->cuGraphKernelNodeSetAttribute = (PFNCUGRAPHKERNELNODESETATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphKernelNodeSetAttribute")));
	funcList->cuOccupancyMaxActiveBlocksPerMultiprocessor = (PFNCUOCCUPANCYMAXACTIVEBLOCKSPERMULTIPROCESSOR)(FPlatformProcess::GetDllExport(library, TEXT("cuOccupancyMaxActiveBlocksPerMultiprocessor")));
	funcList->cuOccupancyMaxActiveBlocksPerMultiprocessorWithFlags = (PFNCUOCCUPANCYMAXACTIVEBLOCKSPERMULTIPROCESSORWITHFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuOccupancyMaxActiveBlocksPerMultiprocessorWithFlags")));
	funcList->cuOccupancyMaxPotentialBlockSize = (PFNCUOCCUPANCYMAXPOTENTIALBLOCKSIZE)(FPlatformProcess::GetDllExport(library, TEXT("cuOccupancyMaxPotentialBlockSize")));
	funcList->cuOccupancyMaxPotentialBlockSizeWithFlags = (PFNCUOCCUPANCYMAXPOTENTIALBLOCKSIZEWITHFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuOccupancyMaxPotentialBlockSizeWithFlags")));
	funcList->cuOccupancyAvailableDynamicSMemPerBlock = (PFNCUOCCUPANCYAVAILABLEDYNAMICSMEMPERBLOCK)(FPlatformProcess::GetDllExport(library, TEXT("cuOccupancyAvailableDynamicSMemPerBlock")));
	funcList->cuTexRefSetArray = (PFNCUTEXREFSETARRAY)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetArray")));
	funcList->cuTexRefSetMipmappedArray = (PFNCUTEXREFSETMIPMAPPEDARRAY)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetMipmappedArray")));
	funcList->cuTexRefSetAddress = (PFNCUTEXREFSETADDRESS)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetAddress_v2")));
	funcList->cuTexRefSetAddress2D = (PFNCUTEXREFSETADDRESS2D)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetAddress2D_v3")));
	funcList->cuTexRefSetFormat = (PFNCUTEXREFSETFORMAT)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetFormat")));
	funcList->cuTexRefSetAddressMode = (PFNCUTEXREFSETADDRESSMODE)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetAddressMode")));
	funcList->cuTexRefSetFilterMode = (PFNCUTEXREFSETFILTERMODE)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetFilterMode")));
	funcList->cuTexRefSetMipmapFilterMode = (PFNCUTEXREFSETMIPMAPFILTERMODE)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetMipmapFilterMode")));
	funcList->cuTexRefSetMipmapLevelBias = (PFNCUTEXREFSETMIPMAPLEVELBIAS)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetMipmapLevelBias")));
	funcList->cuTexRefSetMipmapLevelClamp = (PFNCUTEXREFSETMIPMAPLEVELCLAMP)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetMipmapLevelClamp")));
	funcList->cuTexRefSetMaxAnisotropy = (PFNCUTEXREFSETMAXANISOTROPY)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetMaxAnisotropy")));
	funcList->cuTexRefSetBorderColor = (PFNCUTEXREFSETBORDERCOLOR)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetBorderColor")));
	funcList->cuTexRefSetFlags = (PFNCUTEXREFSETFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefSetFlags")));
	funcList->cuTexRefGetAddress = (PFNCUTEXREFGETADDRESS)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetAddress_v2")));
	funcList->cuTexRefGetArray = (PFNCUTEXREFGETARRAY)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetArray")));
	funcList->cuTexRefGetMipmappedArray = (PFNCUTEXREFGETMIPMAPPEDARRAY)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetMipmappedArray")));
	funcList->cuTexRefGetAddressMode = (PFNCUTEXREFGETADDRESSMODE)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetAddressMode")));
	funcList->cuTexRefGetFilterMode = (PFNCUTEXREFGETFILTERMODE)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetFilterMode")));
	funcList->cuTexRefGetFormat = (PFNCUTEXREFGETFORMAT)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetFormat")));
	funcList->cuTexRefGetMipmapFilterMode = (PFNCUTEXREFGETMIPMAPFILTERMODE)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetMipmapFilterMode")));
	funcList->cuTexRefGetMipmapLevelBias = (PFNCUTEXREFGETMIPMAPLEVELBIAS)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetMipmapLevelBias")));
	funcList->cuTexRefGetMipmapLevelClamp = (PFNCUTEXREFGETMIPMAPLEVELCLAMP)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetMipmapLevelClamp")));
	funcList->cuTexRefGetMaxAnisotropy = (PFNCUTEXREFGETMAXANISOTROPY)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetMaxAnisotropy")));
	funcList->cuTexRefGetBorderColor = (PFNCUTEXREFGETBORDERCOLOR)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetBorderColor")));
	funcList->cuTexRefGetFlags = (PFNCUTEXREFGETFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefGetFlags")));
	funcList->cuTexRefCreate = (PFNCUTEXREFCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefCreate")));
	funcList->cuTexRefDestroy = (PFNCUTEXREFDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuTexRefDestroy")));
	funcList->cuSurfRefSetArray = (PFNCUSURFREFSETARRAY)(FPlatformProcess::GetDllExport(library, TEXT("cuSurfRefSetArray")));
	funcList->cuSurfRefGetArray = (PFNCUSURFREFGETARRAY)(FPlatformProcess::GetDllExport(library, TEXT("cuSurfRefGetArray")));
	funcList->cuTexObjectCreate = (PFNCUTEXOBJECTCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuTexObjectCreate")));
	funcList->cuTexObjectDestroy = (PFNCUTEXOBJECTDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuTexObjectDestroy")));
	funcList->cuTexObjectGetResourceDesc = (PFNCUTEXOBJECTGETRESOURCEDESC)(FPlatformProcess::GetDllExport(library, TEXT("cuTexObjectGetResourceDesc")));
	funcList->cuTexObjectGetTextureDesc = (PFNCUTEXOBJECTGETTEXTUREDESC)(FPlatformProcess::GetDllExport(library, TEXT("cuTexObjectGetTextureDesc")));
	funcList->cuTexObjectGetResourceViewDesc = (PFNCUTEXOBJECTGETRESOURCEVIEWDESC)(FPlatformProcess::GetDllExport(library, TEXT("cuTexObjectGetResourceViewDesc")));
	funcList->cuSurfObjectCreate = (PFNCUSURFOBJECTCREATE)(FPlatformProcess::GetDllExport(library, TEXT("cuSurfObjectCreate")));
	funcList->cuSurfObjectDestroy = (PFNCUSURFOBJECTDESTROY)(FPlatformProcess::GetDllExport(library, TEXT("cuSurfObjectDestroy")));
	funcList->cuSurfObjectGetResourceDesc = (PFNCUSURFOBJECTGETRESOURCEDESC)(FPlatformProcess::GetDllExport(library, TEXT("cuSurfObjectGetResourceDesc")));
	funcList->cuDeviceCanAccessPeer = (PFNCUDEVICECANACCESSPEER)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceCanAccessPeer")));
	funcList->cuCtxEnablePeerAccess = (PFNCUCTXENABLEPEERACCESS)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxEnablePeerAccess")));
	funcList->cuCtxDisablePeerAccess = (PFNCUCTXDISABLEPEERACCESS)(FPlatformProcess::GetDllExport(library, TEXT("cuCtxDisablePeerAccess")));
	funcList->cuDeviceGetP2PAttribute = (PFNCUDEVICEGETP2PATTRIBUTE)(FPlatformProcess::GetDllExport(library, TEXT("cuDeviceGetP2PAttribute")));
	funcList->cuGraphicsUnregisterResource = (PFNCUGRAPHICSUNREGISTERRESOURCE)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphicsUnregisterResource")));
	funcList->cuGraphicsSubResourceGetMappedArray = (PFNCUGRAPHICSSUBRESOURCEGETMAPPEDARRAY)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphicsSubResourceGetMappedArray")));
	funcList->cuGraphicsResourceGetMappedMipmappedArray = (PFNCUGRAPHICSRESOURCEGETMAPPEDMIPMAPPEDARRAY)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphicsResourceGetMappedMipmappedArray")));
	funcList->cuGraphicsResourceGetMappedPointer = (PFNCUGRAPHICSRESOURCEGETMAPPEDPOINTER)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphicsResourceGetMappedPointer_v2")));
	funcList->cuGraphicsResourceSetMapFlags = (PFNCUGRAPHICSRESOURCESETMAPFLAGS)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphicsResourceSetMapFlags_v2")));
	funcList->cuGraphicsMapResources = (PFNCUGRAPHICSMAPRESOURCES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphicsMapResources")));
	funcList->cuGraphicsUnmapResources = (PFNCUGRAPHICSUNMAPRESOURCES)(FPlatformProcess::GetDllExport(library, TEXT("cuGraphicsUnmapResources")));
	funcList->cuGetExportTable = (PFNCUGETEXPORTTABLE)(FPlatformProcess::GetDllExport(library, TEXT("cuGetExportTable")));
	funcList->cuFuncGetModule = (PFNCUFUNCGETMODULE)(FPlatformProcess::GetDllExport(library, TEXT("cuFuncGetModule")));
	
	return true;
}

void CloseCudaLibrary(void* library) {
	FPlatformProcess::FreeDllHandle(library);
}
