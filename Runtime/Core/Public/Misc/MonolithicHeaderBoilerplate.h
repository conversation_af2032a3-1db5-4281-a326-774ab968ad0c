// Copyright Epic Games, Inc. All Rights Reserved.

#include "CoreTypes.h"

#if UE_IS_ENGINE_MODULE && !defined(SUPPRESS_MONOLITHIC_HEADER_WARNINGS) && !defined(UE_DIRECT_HEADER_COMPILE)
	#define MON<PERSON><PERSON>HIC_HEADER_BOILERPLATE() COMPILE_WARNING("Monolithic headers should not be used by this module. Please change it to explicitly include the headers it needs.")
	#define SUPPRESS_MONOLITHIC_HEADER_WARNINGS 1
#else
	#undef MONOLITHIC_HEADER_BOILERPLATE
	#define MON<PERSON>ITHIC_HEADER_BOILERPLATE()
#endif