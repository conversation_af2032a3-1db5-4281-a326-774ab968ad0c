// Copyright Epic Games, Inc. All Rights Reserved.

/**
 * MaterialExpressionMaterialFunctionCall - an expression which allows a material to use a material function
 */

#pragma once

#include "CoreMinimal.h"
#include "UObject/ObjectMacros.h"
#include "Misc/Guid.h"
#include "MaterialExpressionIO.h"
#include "MaterialTypes.h"
#include "Materials/MaterialExpression.h"
#include "MaterialExpressionMaterialFunctionCall.generated.h"

class UMaterialFunction;
class FMaterialFunctionCompileState;
struct FPropertyChangedEvent;

/** Struct that stores information about a function input which is needed to maintain connections and implement the function call. */
USTRUCT()
struct FFunctionExpressionInput
{
	GENERATED_USTRUCT_BODY()

	/** 
	 * Reference to the FunctionInput in the material function.  
	 * This is a reference to a private object so it can't be saved, and must be generated by UpdateFromFunctionResource or SetMaterialFunction. 
	 */
	UPROPERTY(transient)
	TObjectPtr<class UMaterialExpressionFunctionInput> ExpressionInput;

	/** Id of the FunctionInput, used to link ExpressionInput. */
	UPROPERTY()
	FGuid ExpressionInputId;

	/** Actual input struct which stores information about how this input is connected in the material. */
	UPROPERTY()
	FExpressionInput Input;


	FFunctionExpressionInput()
		: ExpressionInput(NULL)
	{
	}

};

/** Struct that stores information about a function output which is needed to maintain connections and implement the function call. */
USTRUCT()
struct FFunctionExpressionOutput
{
	GENERATED_USTRUCT_BODY()

	/** 
	 * Reference to the FunctionOutput in the material function.  
	 * This is a reference to a private object so it can't be saved, and must be generated by UpdateFromFunctionResource or SetMaterialFunction. 
	 */
	UPROPERTY(transient)
	TObjectPtr<class UMaterialExpressionFunctionOutput> ExpressionOutput;

	/** Id of the FunctionOutput, used to link ExpressionOutput. */
	UPROPERTY()
	FGuid ExpressionOutputId;

	/** Actual output struct which stores information about how this output is connected in the material. */
	UPROPERTY()
	FExpressionOutput Output;


	FFunctionExpressionOutput()
		: ExpressionOutput(NULL)
	{
	}

};

UCLASS(hidecategories=object, MinimalAPI)
class UMaterialExpressionMaterialFunctionCall : public UMaterialExpression
{
	GENERATED_UCLASS_BODY()

	/** The function to call. */
	UPROPERTY(EditAnywhere, Category=MaterialExpressionMaterialFunctionCall, meta=(DisallowedClasses="/Script/Engine.MaterialFunctionInstance"))
	TObjectPtr<class UMaterialFunctionInterface> MaterialFunction;

	/** Array of all the function inputs that this function exposes. */
	UPROPERTY()
	TArray<struct FFunctionExpressionInput> FunctionInputs;

	/** Array of all the function outputs that this function exposes. */
	UPROPERTY()
	TArray<struct FFunctionExpressionOutput> FunctionOutputs;

	/** Used by material parameters to split references to separate instances. */
	UPROPERTY(Transient)
	struct FMaterialParameterInfo FunctionParameterInfo;

	//~ Begin UObject Interface.
#if WITH_EDITOR
	virtual void PreEditChange(FProperty* PropertyAboutToChange) override;
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif // WITH_EDITOR
	virtual void PostLoad() override;
	//~ End UObject Interface.

#if WITH_EDITORONLY_DATA
	ENGINE_API bool IterateDependentFunctions(TFunctionRef<bool(UMaterialFunctionInterface*)> Predicate) const;
	ENGINE_API void GetDependentFunctions(TArray<UMaterialFunctionInterface*>& DependentFunctions) const;
#endif

#if WITH_EDITOR
	ENGINE_API void UnlinkFunctionFromCaller(FMaterialCompiler* Compiler);
	ENGINE_API void LinkFunctionIntoCaller(FMaterialCompiler* Compiler);
#endif

	//~ Begin UMaterialExpression Interface
#if WITH_EDITOR
	virtual int32 Compile(class FMaterialCompiler* Compiler, int32 OutputIndex) override;
	virtual void GetCaption(TArray<FString>& OutCaptions) const override;
	virtual TArrayView<FExpressionInput*> GetInputsView() override;
	virtual FExpressionInput* GetInput(int32 InputIndex) override;
	virtual FName GetInputName(int32 InputIndex) const override;
	virtual bool IsInputConnectionRequired(int32 InputIndex) const override;
	virtual FString GetDescription() const override;
	virtual void GetConnectorToolTip(int32 InputIndex, int32 OutputIndex, TArray<FString>& OutToolTip) override;
	virtual void GetExpressionToolTip(TArray<FString>& OutToolTip) override;
	virtual bool MatchesSearchQuery( const TCHAR* SearchQuery ) override;
	virtual bool IsResultMaterialAttributes(int32 OutputIndex) override;
	virtual bool IsResultSubstrateMaterial(int32 OutputIndex) override;
	virtual void GatherSubstrateMaterialInfo(FSubstrateMaterialInfo& SubstrateMaterialInfo, int32 OutputIndex) override;
	virtual FSubstrateOperator* SubstrateGenerateMaterialTopologyTree(class FMaterialCompiler* Compiler, class UMaterialExpression* Parent, int32 OutputIndex) override;
	virtual uint32 GetInputType(int32 InputIndex) override;
	//~ End UMaterialExpression Interface

	/** Util to get name of a particular type, optionally with type appended in parenthesis */
	ENGINE_API FName GetInputNameWithType(int32 InputIndex, bool bWithType) const;

	/** 
	 * Set a new material function, given an old function so that links can be passed over if the name matches. 
	 *
	 *	@param OldFunctionResource				The function it was set to.
	 *	@param NewResource						The function to be set to.
	 *
	 *	@return									true if setting the function was a success, false if it failed.
	 */
	ENGINE_API bool SetMaterialFunctionEx(UMaterialFunctionInterface* OldFunctionResource, UMaterialFunctionInterface* NewResource);

	/** */
	UFUNCTION(BlueprintCallable, Category = "MaterialEditing")
	ENGINE_API bool SetMaterialFunction(UMaterialFunctionInterface* NewMaterialFunction);

	/** 
	 * Update FunctionInputs and FunctionOutputs from the MaterialFunction.  
	 * This must be called to keep the inputs and outputs up to date with the function being used. 
	 */
	ENGINE_API void UpdateFromFunctionResource(bool bRecreateAndLinkNode = true);

	/** Temporary compilation state shared between function calls */
	void SetSharedCompileState(FMaterialFunctionCompileState* SharedState)
	{
		SharedCompileState = SharedState;
	}

	virtual bool GenerateHLSLExpression(FMaterialHLSLGenerator& Generator, UE::HLSLTree::FScope& Scope, int32 OutputIndex, UE::HLSLTree::FExpression const*& OutExpression) const override;

private:	
	/** Helper that fixes up expression links where possible. */
	void FixupReferencingExpressions(
		const TArray<FFunctionExpressionOutput>& NewOutputs,
		const TArray<FFunctionExpressionOutput>& OriginalOutputs,
		TConstArrayView<TObjectPtr<UMaterialExpression>> Expressions, 
		TArray<FExpressionInput*>& MaterialInputs,
		bool bMatchByName);

	/** Temporary compilation state shared between function calls */
	FMaterialFunctionCompileState* SharedCompileState;

	/** Stashed data between a Pre/PostEditChange event */
	UMaterialFunctionInterface* SavedMaterialFunction = nullptr;
#endif // WITH_EDITOR
};



