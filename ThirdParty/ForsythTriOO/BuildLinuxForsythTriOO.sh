#!/bin/bash

set -eu

SCRIPT_DIR=$(cd "$(dirname "$BASH_SOURCE")" ; pwd)
THIRD_PARTY=$(cd "${SCRIPT_DIR}/.." ; pwd)

# Get num of cores
export CORES=$(getconf _NPROCESSORS_ONLN)
echo "Using ${CORES} cores for building"

BuildForsythTriOO()
{
    export ARCH=$1
    export FLAVOR=$2
    local BUILD_DIR=${SCRIPT_DIR}/Build-${FLAVOR}.${ARCH}

    echo "Building ${ARCH}"
    rm -rf ${BUILD_DIR}
    mkdir -p ${BUILD_DIR}

    pushd ${BUILD_DIR}

    set -x
    cmake \
      -DCMAKE_TOOLCHAIN_FILE="/tmp/__cmake_toolchain.cmake" \
      -DCMAKE_MAKE_PROGRAM=$(which make) \
      -DCMAKE_BUILD_TYPE=${FLAVOR} \
      ${SCRIPT_DIR}
    set +x

    echo
    VERBOSE=1 make -j ${CORES} --no-print-directory
    echo

    mkdir -p ${SCRIPT_DIR}/Lib/Linux/${ARCH}
    cp *.a ${SCRIPT_DIR}/Lib/Linux/${ARCH}

    popd
}

( cat <<_EOF_
  ## autogenerated by ${BASH_SOURCE} script
  SET(LINUX_MULTIARCH_ROOT \$ENV{LINUX_MULTIARCH_ROOT})
  SET(ARCHITECTURE_TRIPLE \$ENV{ARCH})

  message (STATUS "LINUX_MULTIARCH_ROOT is '\${LINUX_MULTIARCH_ROOT}'")
  message (STATUS "ARCHITECTURE_TRIPLE is '\${ARCHITECTURE_TRIPLE}'")

  SET(CMAKE_CROSSCOMPILING TRUE)
  SET(CMAKE_SYSTEM_NAME Linux)
  SET(CMAKE_SYSTEM_VERSION 1)

  # sysroot
  SET(CMAKE_SYSROOT \${LINUX_MULTIARCH_ROOT}/\${ARCHITECTURE_TRIPLE})

  SET(CMAKE_LIBRARY_ARCHITECTURE \${ARCHITECTURE_TRIPLE})

  # specify the cross compiler
  SET(CMAKE_C_COMPILER            \${CMAKE_SYSROOT}/bin/clang)
  SET(CMAKE_C_COMPILER_TARGET     \${ARCHITECTURE_TRIPLE})
  SET(CMAKE_C_FLAGS "-target      \${ARCHITECTURE_TRIPLE}")

  include_directories("${THIRD_PARTY}/Linux/LibCxx/include")
  include_directories("${THIRD_PARTY}/Linux/LibCxx/include/c++/v1")

  SET(CMAKE_CXX_COMPILER          \${CMAKE_SYSROOT}/bin/clang++)
  SET(CMAKE_CXX_COMPILER_TARGET   \${ARCHITECTURE_TRIPLE})
  SET(CMAKE_CXX_FLAGS "-std=c++11 -nostdinc++")

  SET(CMAKE_ASM_COMPILER          \${CMAKE_SYSROOT}/bin/clang)

  SET(CMAKE_FIND_ROOT_PATH        \${LINUX_MULTIARCH_ROOT})

  # hoping to force it to use ar
  set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM ONLY)
  set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
  set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

_EOF_
) > /tmp/__cmake_toolchain.cmake

BuildForsythTriOO x86_64-unknown-linux-gnu Debug
BuildForsythTriOO x86_64-unknown-linux-gnu Release
BuildForsythTriOO aarch64-unknown-linux-gnueabi Debug
BuildForsythTriOO aarch64-unknown-linux-gnueabi Release
