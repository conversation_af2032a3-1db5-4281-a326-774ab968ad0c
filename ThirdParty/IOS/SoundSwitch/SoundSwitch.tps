<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>SoundSwitch</Name>
  <Location>Engine/Source/ThirdParty/IOS/SoundSwitch</Location>
  <Function>Implemented in IOSAppDelegate.cpp.</Function>
  <Eula>https://github.com/moshegottlieb/SoundSwitch/blob/master/License.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/SoundSwitch_License.txt</LicenseFolder>
</TpsData>