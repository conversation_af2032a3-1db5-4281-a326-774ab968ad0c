<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>OpenImageDenoise </Name>
  <!-- Software Name and Version  -->
<!-- Software Name: OpenImageDenoise 
    Download Link: https://www.openimagedenoise.org/
    Version: v2.3.0-->
<Location>//Fortnite/Main/Engine/Source/ThirdParty/Intel/OIDN/</Location>
<Function>Provides denoising for path traced images</Function>
  <Eula>https://github.com/RenderKit/oidn?tab=Apache-2.0-1-ov-file#readme</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 


 