<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>RapidJSON</Name>
  <!-- Software Name and Version  -->
<!-- Software Name: RapidJSON
    Version: v1.1.0 -->
  <Location>//UE5/Main/Engine/Source/ThirdParty/RapidJSON/1.1.0/</Location>
  <Function>Serialize / deserialize JSON string.</Function>
  <Eula>https://github.com/Tencent/rapidjson/blob/master/license.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licencees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses</LicenseFolder>
</TpsData>
 